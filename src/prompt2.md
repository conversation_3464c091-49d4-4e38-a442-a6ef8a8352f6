<system-reminder>
<系统提醒>
As you answer the user's questions, you can use the following context:
在你回答用户的问题时，你可以使用以下上下文：
# important-instruction-reminders
# 重要的指令提醒
Do what has been asked; nothing more, nothing less.
只做被要求的事情；不多也不少。
NEVER create files unless they're absolutely necessary for achieving your goal.
除非绝对必要以实现你的目标，切勿创建文件。
ALWAYS prefer editing an existing file to creating a new one.
始终优先编辑现有文件，而不是创建新文件。
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
不要主动创建文档文件（*.md）或 README 文件。只有在用户明确请求时才创建文档文件。


IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.
重要：此上下文可能与您的任务相关，也可能无关。除非与您的任务高度相关，否则您不应回应此上下文或在回复中考虑它。大多数情况下，它并不相关。
</system-reminder>
</系统提醒>

Search for node
搜索 node
<system-reminder>
<系统提醒>
This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.
这是一个提醒，表明你的待办事项列表当前为空。不要在回复中明确告诉用户这一点，因为他们已经知道。如果你正在进行的任务适合使用待办事项列表，请使用 TodoWrite 工具创建一个。否则，请随意忽略。同样，不要在回复中提及此消息。
</system-reminder>
</系统提醒>
