package com.example.photoupload.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 文件访问拦截器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class FileAccessInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(FileAccessInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        
        logger.info("文件访问请求 - IP: {}, User-Agent: {}, Referer: {}, URI: {}", 
                   clientIp, userAgent, referer, request.getRequestURI());

        // 防盗链检查
        if (isHotlinkingAttempt(request)) {
            logger.warn("检测到盗链访问 - IP: {}, Referer: {}", clientIp, referer);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }

        // IP访问控制
        if (!isIpAllowed(clientIp)) {
            logger.warn("IP访问被拒绝 - IP: {}", clientIp);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }

        return true;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 检查是否为盗链访问
     */
    private boolean isHotlinkingAttempt(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        
        // 如果没有Referer，允许访问（直接访问）
        if (referer == null || referer.isEmpty()) {
            return false;
        }

        // 检查Referer是否来自允许的域名
        String[] allowedDomains = {"localhost", "127.0.0.1"};
        
        for (String domain : allowedDomains) {
            if (referer.contains(domain)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查IP是否被允许访问
     */
    private boolean isIpAllowed(String ip) {
        // 这里可以实现IP白名单/黑名单逻辑
        // 目前允许所有IP访问
        return true;
    }
} 