package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * WebSocket消息DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class WebSocketMessage {
    
    @JsonProperty("type")
    private MessageType type;
    
    @JsonProperty("data")
    private Object data;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("user_id")
    private String userId;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        UPLOAD_PROGRESS,        // 上传进度
        UPLOAD_COMPLETE,        // 上传完成
        UPLOAD_ERROR,          // 上传错误
        AI_ANALYSIS_START,     // AI分析开始
        AI_ANALYSIS_COMPLETE,  // AI分析完成
        AI_ANALYSIS_ERROR,     // AI分析错误
        SYSTEM_NOTIFICATION,   // 系统通知
        USER_NOTIFICATION,     // 用户通知
        FILE_SHARED,          // 文件分享
        FILE_LIKED,           // 文件点赞
        FILE_COMMENTED,       // 文件评论
        BATCH_PROCESS_START,  // 批量处理开始
        BATCH_PROCESS_PROGRESS, // 批量处理进度
        BATCH_PROCESS_COMPLETE  // 批量处理完成
    }

    // 上传进度数据
    public static class UploadProgressData {
        @JsonProperty("file_name")
        private String fileName;
        
        @JsonProperty("progress")
        private Integer progress; // 0-100
        
        @JsonProperty("bytes_uploaded")
        private Long bytesUploaded;
        
        @JsonProperty("total_bytes")
        private Long totalBytes;
        
        @JsonProperty("speed")
        private String speed; // 上传速度，如 "1.2 MB/s"

        // Constructors
        public UploadProgressData() {}
        
        public UploadProgressData(String fileName, Integer progress, Long bytesUploaded, Long totalBytes, String speed) {
            this.fileName = fileName;
            this.progress = progress;
            this.bytesUploaded = bytesUploaded;
            this.totalBytes = totalBytes;
            this.speed = speed;
        }

        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }
        
        public Long getBytesUploaded() { return bytesUploaded; }
        public void setBytesUploaded(Long bytesUploaded) { this.bytesUploaded = bytesUploaded; }
        
        public Long getTotalBytes() { return totalBytes; }
        public void setTotalBytes(Long totalBytes) { this.totalBytes = totalBytes; }
        
        public String getSpeed() { return speed; }
        public void setSpeed(String speed) { this.speed = speed; }
    }

    // AI分析数据
    public static class AIAnalysisData {
        @JsonProperty("file_name")
        private String fileName;
        
        @JsonProperty("analysis_type")
        private String analysisType;
        
        @JsonProperty("progress")
        private Integer progress; // 0-100
        
        @JsonProperty("result")
        private AIAnalysisResult result;
        
        @JsonProperty("error_message")
        private String errorMessage;

        // Constructors
        public AIAnalysisData() {}
        
        public AIAnalysisData(String fileName, String analysisType, Integer progress) {
            this.fileName = fileName;
            this.analysisType = analysisType;
            this.progress = progress;
        }

        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getAnalysisType() { return analysisType; }
        public void setAnalysisType(String analysisType) { this.analysisType = analysisType; }
        
        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }
        
        public AIAnalysisResult getResult() { return result; }
        public void setResult(AIAnalysisResult result) { this.result = result; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    // 系统通知数据
    public static class NotificationData {
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("level")
        private NotificationLevel level;
        
        @JsonProperty("action_url")
        private String actionUrl;
        
        @JsonProperty("metadata")
        private Map<String, Object> metadata;

        public enum NotificationLevel {
            INFO, SUCCESS, WARNING, ERROR
        }

        // Constructors
        public NotificationData() {}
        
        public NotificationData(String title, String message, NotificationLevel level) {
            this.title = title;
            this.message = message;
            this.level = level;
        }

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public NotificationLevel getLevel() { return level; }
        public void setLevel(NotificationLevel level) { this.level = level; }
        
        public String getActionUrl() { return actionUrl; }
        public void setActionUrl(String actionUrl) { this.actionUrl = actionUrl; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    // 批量处理数据
    public static class BatchProcessData {
        @JsonProperty("batch_id")
        private String batchId;
        
        @JsonProperty("total_files")
        private Integer totalFiles;
        
        @JsonProperty("processed_files")
        private Integer processedFiles;
        
        @JsonProperty("current_file")
        private String currentFile;
        
        @JsonProperty("operation")
        private String operation;
        
        @JsonProperty("progress")
        private Integer progress; // 0-100

        // Constructors
        public BatchProcessData() {}
        
        public BatchProcessData(String batchId, Integer totalFiles, Integer processedFiles, String operation) {
            this.batchId = batchId;
            this.totalFiles = totalFiles;
            this.processedFiles = processedFiles;
            this.operation = operation;
            this.progress = totalFiles > 0 ? (processedFiles * 100 / totalFiles) : 0;
        }

        // Getters and Setters
        public String getBatchId() { return batchId; }
        public void setBatchId(String batchId) { this.batchId = batchId; }
        
        public Integer getTotalFiles() { return totalFiles; }
        public void setTotalFiles(Integer totalFiles) { this.totalFiles = totalFiles; }
        
        public Integer getProcessedFiles() { return processedFiles; }
        public void setProcessedFiles(Integer processedFiles) { this.processedFiles = processedFiles; }
        
        public String getCurrentFile() { return currentFile; }
        public void setCurrentFile(String currentFile) { this.currentFile = currentFile; }
        
        public String getOperation() { return operation; }
        public void setOperation(String operation) { this.operation = operation; }
        
        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }
    }

    // Constructors
    public WebSocketMessage() {
        this.timestamp = LocalDateTime.now();
    }
    
    public WebSocketMessage(MessageType type, Object data) {
        this.type = type;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }
    
    public WebSocketMessage(MessageType type, Object data, String sessionId, String userId) {
        this.type = type;
        this.data = data;
        this.sessionId = sessionId;
        this.userId = userId;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public MessageType getType() { return type; }
    public void setType(MessageType type) { this.type = type; }
    
    public Object getData() { return data; }
    public void setData(Object data) { this.data = data; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
}
