package com.example.photoupload.dto;

import java.util.List;

/**
 * 色彩分析结果DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ColorAnalysisResult {
    
    private String fileName;
    private List<DominantColor> dominantColors;
    private ColorStatistics statistics;
    private String mood;
    private Double temperature; // 色温 (冷暖)
    private Double saturation; // 饱和度
    private Double brightness; // 亮度
    
    public ColorAnalysisResult() {}
    
    public ColorAnalysisResult(String fileName) {
        this.fileName = fileName;
    }
    
    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public List<DominantColor> getDominantColors() { return dominantColors; }
    public void setDominantColors(List<DominantColor> dominantColors) { this.dominantColors = dominantColors; }
    
    public ColorStatistics getStatistics() { return statistics; }
    public void setStatistics(ColorStatistics statistics) { this.statistics = statistics; }
    
    public String getMood() { return mood; }
    public void setMood(String mood) { this.mood = mood; }
    
    public Double getTemperature() { return temperature; }
    public void setTemperature(Double temperature) { this.temperature = temperature; }
    
    public Double getSaturation() { return saturation; }
    public void setSaturation(Double saturation) { this.saturation = saturation; }
    
    public Double getBrightness() { return brightness; }
    public void setBrightness(Double brightness) { this.brightness = brightness; }
    
    /**
     * 主要颜色
     */
    public static class DominantColor {
        private String hex;
        private RGB rgb;
        private HSL hsl;
        private Double percentage;
        private String name;
        
        public DominantColor() {}
        
        public DominantColor(String hex, RGB rgb, Double percentage) {
            this.hex = hex;
            this.rgb = rgb;
            this.percentage = percentage;
        }
        
        public String getHex() { return hex; }
        public void setHex(String hex) { this.hex = hex; }
        
        public RGB getRgb() { return rgb; }
        public void setRgb(RGB rgb) { this.rgb = rgb; }
        
        public HSL getHsl() { return hsl; }
        public void setHsl(HSL hsl) { this.hsl = hsl; }
        
        public Double getPercentage() { return percentage; }
        public void setPercentage(Double percentage) { this.percentage = percentage; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
    
    /**
     * RGB颜色值
     */
    public static class RGB {
        private int r, g, b;
        
        public RGB() {}
        
        public RGB(int r, int g, int b) {
            this.r = r; this.g = g; this.b = b;
        }
        
        public int getR() { return r; }
        public void setR(int r) { this.r = r; }
        
        public int getG() { return g; }
        public void setG(int g) { this.g = g; }
        
        public int getB() { return b; }
        public void setB(int b) { this.b = b; }
    }
    
    /**
     * HSL颜色值
     */
    public static class HSL {
        private double h, s, l;
        
        public HSL() {}
        
        public HSL(double h, double s, double l) {
            this.h = h; this.s = s; this.l = l;
        }
        
        public double getH() { return h; }
        public void setH(double h) { this.h = h; }
        
        public double getS() { return s; }
        public void setS(double s) { this.s = s; }
        
        public double getL() { return l; }
        public void setL(double l) { this.l = l; }
    }
    
    /**
     * 色彩统计信息
     */
    public static class ColorStatistics {
        private int totalColors;
        private int uniqueColors;
        private Double averageHue;
        private Double averageSaturation;
        private Double averageLightness;
        private boolean isMonochrome;
        private String colorHarmony; // 色彩和谐度类型
        
        public ColorStatistics() {}
        
        public int getTotalColors() { return totalColors; }
        public void setTotalColors(int totalColors) { this.totalColors = totalColors; }
        
        public int getUniqueColors() { return uniqueColors; }
        public void setUniqueColors(int uniqueColors) { this.uniqueColors = uniqueColors; }
        
        public Double getAverageHue() { return averageHue; }
        public void setAverageHue(Double averageHue) { this.averageHue = averageHue; }
        
        public Double getAverageSaturation() { return averageSaturation; }
        public void setAverageSaturation(Double averageSaturation) { this.averageSaturation = averageSaturation; }
        
        public Double getAverageLightness() { return averageLightness; }
        public void setAverageLightness(Double averageLightness) { this.averageLightness = averageLightness; }
        
        public boolean isMonochrome() { return isMonochrome; }
        public void setMonochrome(boolean monochrome) { isMonochrome = monochrome; }
        
        public String getColorHarmony() { return colorHarmony; }
        public void setColorHarmony(String colorHarmony) { this.colorHarmony = colorHarmony; }
    }
} 