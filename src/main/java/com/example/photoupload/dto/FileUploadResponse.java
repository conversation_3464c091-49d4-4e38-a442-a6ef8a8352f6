package com.example.photoupload.dto;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileUploadResponse {

    private Long id;
    private String originalName;
    private String storedName;
    private Long fileSize;
    private String contentType;
    private String downloadUrl;
    private String previewUrl;
    private String thumbnailUrl;
    private boolean isCompressed;
    private Integer imageWidth;
    private Integer imageHeight;
    private String md5Hash;

    // 无参构造函数
    public FileUploadResponse() {}

    // 全参构造函数
    public FileUploadResponse(Long id, String originalName, String storedName, Long fileSize, 
                             String contentType, String downloadUrl, String previewUrl, 
                             String thumbnailUrl, boolean isCompressed, Integer imageWidth, 
                             Integer imageHeight, String md5Hash) {
        this.id = id;
        this.originalName = originalName;
        this.storedName = storedName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.downloadUrl = downloadUrl;
        this.previewUrl = previewUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.isCompressed = isCompressed;
        this.imageWidth = imageWidth;
        this.imageHeight = imageHeight;
        this.md5Hash = md5Hash;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getStoredName() {
        return storedName;
    }

    public void setStoredName(String storedName) {
        this.storedName = storedName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public boolean isCompressed() {
        return isCompressed;
    }

    public void setCompressed(boolean compressed) {
        isCompressed = compressed;
    }

    public Integer getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }

    public Integer getImageHeight() {
        return imageHeight;
    }

    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }

    public String getMd5Hash() {
        return md5Hash;
    }

    public void setMd5Hash(String md5Hash) {
        this.md5Hash = md5Hash;
    }
} 