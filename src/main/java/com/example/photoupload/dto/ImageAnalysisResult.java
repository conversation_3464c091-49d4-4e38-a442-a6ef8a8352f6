package com.example.photoupload.dto;

import java.util.List;
import java.util.Map;

/**
 * 图片AI分析结果DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ImageAnalysisResult {
    
    private String fileName;
    private List<String> tags;
    private List<DetectedObject> objects;
    private Map<String, Double> emotions;
    private ImageMetadata metadata;
    private Double qualityScore;
    private String description;
    
    public ImageAnalysisResult() {}
    
    public ImageAnalysisResult(String fileName) {
        this.fileName = fileName;
    }
    
    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }
    
    public List<DetectedObject> getObjects() { return objects; }
    public void setObjects(List<DetectedObject> objects) { this.objects = objects; }
    
    public Map<String, Double> getEmotions() { return emotions; }
    public void setEmotions(Map<String, Double> emotions) { this.emotions = emotions; }
    
    public ImageMetadata getMetadata() { return metadata; }
    public void setMetadata(ImageMetadata metadata) { this.metadata = metadata; }
    
    public Double getQualityScore() { return qualityScore; }
    public void setQualityScore(Double qualityScore) { this.qualityScore = qualityScore; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    /**
     * 检测到的对象
     */
    public static class DetectedObject {
        private String name;
        private Double confidence;
        private BoundingBox boundingBox;
        
        public DetectedObject() {}
        
        public DetectedObject(String name, Double confidence, BoundingBox boundingBox) {
            this.name = name;
            this.confidence = confidence;
            this.boundingBox = boundingBox;
        }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Double getConfidence() { return confidence; }
        public void setConfidence(Double confidence) { this.confidence = confidence; }
        
        public BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(BoundingBox boundingBox) { this.boundingBox = boundingBox; }
    }
    
    /**
     * 边界框
     */
    public static class BoundingBox {
        private int x, y, width, height;
        
        public BoundingBox() {}
        
        public BoundingBox(int x, int y, int width, int height) {
            this.x = x; this.y = y; this.width = width; this.height = height;
        }
        
        public int getX() { return x; }
        public void setX(int x) { this.x = x; }
        
        public int getY() { return y; }
        public void setY(int y) { this.y = y; }
        
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
    }
    
    /**
     * 图片元数据
     */
    public static class ImageMetadata {
        private int width, height;
        private String format;
        private long fileSize;
        private String colorSpace;
        private boolean hasTransparency;
        
        public ImageMetadata() {}
        
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public String getColorSpace() { return colorSpace; }
        public void setColorSpace(String colorSpace) { this.colorSpace = colorSpace; }
        
        public boolean isHasTransparency() { return hasTransparency; }
        public void setHasTransparency(boolean hasTransparency) { this.hasTransparency = hasTransparency; }
    }
} 