package com.example.photoupload.dto;

import java.util.List;

/**
 * 批量处理请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class BatchProcessRequest {
    
    private List<String> fileNames;
    private String operation; // filter, watermark, convert, rotate
    private BatchProcessConfig config;
    
    public BatchProcessRequest() {}
    
    public BatchProcessRequest(List<String> fileNames, String operation, BatchProcessConfig config) {
        this.fileNames = fileNames;
        this.operation = operation;
        this.config = config;
    }
    
    // Getters and Setters
    public List<String> getFileNames() {
        return fileNames;
    }
    
    public void setFileNames(List<String> fileNames) {
        this.fileNames = fileNames;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public BatchProcessConfig getConfig() {
        return config;
    }
    
    public void setConfig(BatchProcessConfig config) {
        this.config = config;
    }
    
    /**
     * 批量处理配置内部类
     */
    public static class BatchProcessConfig {
        // 滤镜配置
        private String filterType;
        
        // 水印配置
        private String watermarkText;
        private String watermarkPosition;
        private Float watermarkOpacity;
        private Integer watermarkFontSize;
        private String watermarkFontColor;
        
        // 格式转换配置
        private String targetFormat;
        
        // 旋转配置
        private Double rotateAngle;
        
        // Getters and Setters
        public String getFilterType() { return filterType; }
        public void setFilterType(String filterType) { this.filterType = filterType; }
        
        public String getWatermarkText() { return watermarkText; }
        public void setWatermarkText(String watermarkText) { this.watermarkText = watermarkText; }
        
        public String getWatermarkPosition() { return watermarkPosition; }
        public void setWatermarkPosition(String watermarkPosition) { this.watermarkPosition = watermarkPosition; }
        
        public Float getWatermarkOpacity() { return watermarkOpacity; }
        public void setWatermarkOpacity(Float watermarkOpacity) { this.watermarkOpacity = watermarkOpacity; }
        
        public Integer getWatermarkFontSize() { return watermarkFontSize; }
        public void setWatermarkFontSize(Integer watermarkFontSize) { this.watermarkFontSize = watermarkFontSize; }
        
        public String getWatermarkFontColor() { return watermarkFontColor; }
        public void setWatermarkFontColor(String watermarkFontColor) { this.watermarkFontColor = watermarkFontColor; }
        
        public String getTargetFormat() { return targetFormat; }
        public void setTargetFormat(String targetFormat) { this.targetFormat = targetFormat; }
        
        public Double getRotateAngle() { return rotateAngle; }
        public void setRotateAngle(Double rotateAngle) { this.rotateAngle = rotateAngle; }
    }
} 