package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

/**
 * AI图像分析结果DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AIAnalysisResult {
    
    @JsonProperty("labels")
    private List<Label> labels;
    
    @JsonProperty("objects")
    private List<DetectedObject> objects;
    
    @JsonProperty("faces")
    private List<Face> faces;
    
    @JsonProperty("text")
    private List<TextDetection> textDetections;
    
    @JsonProperty("colors")
    private ColorAnalysis colorAnalysis;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
    
    @JsonProperty("confidence")
    private Double overallConfidence;
    
    @JsonProperty("processing_time")
    private Long processingTime;

    // 标签识别结果
    public static class Label {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("category")
        private String category;

        // Constructors
        public Label() {}
        
        public Label(String name, Double confidence, String category) {
            this.name = name;
            this.confidence = confidence;
            this.category = category;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Double getConfidence() { return confidence; }
        public void setConfidence(Double confidence) { this.confidence = confidence; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
    }

    // 物体检测结果
    public static class DetectedObject {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("bounding_box")
        private BoundingBox boundingBox;

        public static class BoundingBox {
            @JsonProperty("x")
            private Integer x;
            
            @JsonProperty("y")
            private Integer y;
            
            @JsonProperty("width")
            private Integer width;
            
            @JsonProperty("height")
            private Integer height;

            // Constructors
            public BoundingBox() {}
            
            public BoundingBox(Integer x, Integer y, Integer width, Integer height) {
                this.x = x;
                this.y = y;
                this.width = width;
                this.height = height;
            }

            // Getters and Setters
            public Integer getX() { return x; }
            public void setX(Integer x) { this.x = x; }
            
            public Integer getY() { return y; }
            public void setY(Integer y) { this.y = y; }
            
            public Integer getWidth() { return width; }
            public void setWidth(Integer width) { this.width = width; }
            
            public Integer getHeight() { return height; }
            public void setHeight(Integer height) { this.height = height; }
        }

        // Constructors
        public DetectedObject() {}
        
        public DetectedObject(String name, Double confidence, BoundingBox boundingBox) {
            this.name = name;
            this.confidence = confidence;
            this.boundingBox = boundingBox;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Double getConfidence() { return confidence; }
        public void setConfidence(Double confidence) { this.confidence = confidence; }
        
        public BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(BoundingBox boundingBox) { this.boundingBox = boundingBox; }
    }

    // 人脸检测结果
    public static class Face {
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("bounding_box")
        private DetectedObject.BoundingBox boundingBox;
        
        @JsonProperty("age_range")
        private AgeRange ageRange;
        
        @JsonProperty("gender")
        private String gender;
        
        @JsonProperty("emotions")
        private Map<String, Double> emotions;

        public static class AgeRange {
            @JsonProperty("low")
            private Integer low;
            
            @JsonProperty("high")
            private Integer high;

            // Constructors
            public AgeRange() {}
            
            public AgeRange(Integer low, Integer high) {
                this.low = low;
                this.high = high;
            }

            // Getters and Setters
            public Integer getLow() { return low; }
            public void setLow(Integer low) { this.low = low; }
            
            public Integer getHigh() { return high; }
            public void setHigh(Integer high) { this.high = high; }
        }

        // Constructors
        public Face() {}

        // Getters and Setters
        public Double getConfidence() { return confidence; }
        public void setConfidence(Double confidence) { this.confidence = confidence; }
        
        public DetectedObject.BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(DetectedObject.BoundingBox boundingBox) { this.boundingBox = boundingBox; }
        
        public AgeRange getAgeRange() { return ageRange; }
        public void setAgeRange(AgeRange ageRange) { this.ageRange = ageRange; }
        
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        
        public Map<String, Double> getEmotions() { return emotions; }
        public void setEmotions(Map<String, Double> emotions) { this.emotions = emotions; }
    }

    // 文字检测结果
    public static class TextDetection {
        @JsonProperty("text")
        private String text;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("bounding_box")
        private DetectedObject.BoundingBox boundingBox;

        // Constructors
        public TextDetection() {}
        
        public TextDetection(String text, Double confidence, DetectedObject.BoundingBox boundingBox) {
            this.text = text;
            this.confidence = confidence;
            this.boundingBox = boundingBox;
        }

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public Double getConfidence() { return confidence; }
        public void setConfidence(Double confidence) { this.confidence = confidence; }
        
        public DetectedObject.BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(DetectedObject.BoundingBox boundingBox) { this.boundingBox = boundingBox; }
    }

    // 颜色分析结果
    public static class ColorAnalysis {
        @JsonProperty("dominant_colors")
        private List<DominantColor> dominantColors;
        
        @JsonProperty("color_palette")
        private List<String> colorPalette;

        public static class DominantColor {
            @JsonProperty("color")
            private String color;
            
            @JsonProperty("percentage")
            private Double percentage;
            
            @JsonProperty("rgb")
            private RGB rgb;

            public static class RGB {
                @JsonProperty("r")
                private Integer r;
                
                @JsonProperty("g")
                private Integer g;
                
                @JsonProperty("b")
                private Integer b;

                // Constructors
                public RGB() {}
                
                public RGB(Integer r, Integer g, Integer b) {
                    this.r = r;
                    this.g = g;
                    this.b = b;
                }

                // Getters and Setters
                public Integer getR() { return r; }
                public void setR(Integer r) { this.r = r; }
                
                public Integer getG() { return g; }
                public void setG(Integer g) { this.g = g; }
                
                public Integer getB() { return b; }
                public void setB(Integer b) { this.b = b; }
            }

            // Constructors
            public DominantColor() {}
            
            public DominantColor(String color, Double percentage, RGB rgb) {
                this.color = color;
                this.percentage = percentage;
                this.rgb = rgb;
            }

            // Getters and Setters
            public String getColor() { return color; }
            public void setColor(String color) { this.color = color; }
            
            public Double getPercentage() { return percentage; }
            public void setPercentage(Double percentage) { this.percentage = percentage; }
            
            public RGB getRgb() { return rgb; }
            public void setRgb(RGB rgb) { this.rgb = rgb; }
        }

        // Constructors
        public ColorAnalysis() {}

        // Getters and Setters
        public List<DominantColor> getDominantColors() { return dominantColors; }
        public void setDominantColors(List<DominantColor> dominantColors) { this.dominantColors = dominantColors; }
        
        public List<String> getColorPalette() { return colorPalette; }
        public void setColorPalette(List<String> colorPalette) { this.colorPalette = colorPalette; }
    }

    // Constructors
    public AIAnalysisResult() {}

    // Getters and Setters
    public List<Label> getLabels() { return labels; }
    public void setLabels(List<Label> labels) { this.labels = labels; }
    
    public List<DetectedObject> getObjects() { return objects; }
    public void setObjects(List<DetectedObject> objects) { this.objects = objects; }
    
    public List<Face> getFaces() { return faces; }
    public void setFaces(List<Face> faces) { this.faces = faces; }
    
    public List<TextDetection> getTextDetections() { return textDetections; }
    public void setTextDetections(List<TextDetection> textDetections) { this.textDetections = textDetections; }
    
    public ColorAnalysis getColorAnalysis() { return colorAnalysis; }
    public void setColorAnalysis(ColorAnalysis colorAnalysis) { this.colorAnalysis = colorAnalysis; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public Double getOverallConfidence() { return overallConfidence; }
    public void setOverallConfidence(Double overallConfidence) { this.overallConfidence = overallConfidence; }
    
    public Long getProcessingTime() { return processingTime; }
    public void setProcessingTime(Long processingTime) { this.processingTime = processingTime; }
}
