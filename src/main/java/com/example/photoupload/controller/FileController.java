package com.example.photoupload.controller;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.dto.ApiResponse;
import com.example.photoupload.dto.BatchProcessRequest;
import com.example.photoupload.dto.FileInfoResponse;
import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.service.AIImageAnalysisService;
import com.example.photoupload.service.FileService;
import com.example.photoupload.service.WebSocketNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 文件上传下载控制器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/files")
@Tag(name = "文件管理", description = "文件上传、下载、预览等接口")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

    @Autowired
    private AIImageAnalysisService aiImageAnalysisService;

    @Autowired
    private WebSocketNotificationService notificationService;

    /**
     * 上传单个文件
     * 
     * @param file 要上传的文件，通过表单参数传递
     * @param request HTTP请求对象，用于获取客户端IP地址
     * @return 包含上传结果的响应实体，成功时返回文件信息，失败时返回错误信息
     */
    @PostMapping("/upload")
    @Operation(summary = "上传单个文件", description = "支持图片文件上传，自动生成缩略图和压缩")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "上传成功"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "上传失败")
    })
    public ResponseEntity<ApiResponse<FileUploadResponse>> uploadFile(
            @Parameter(description = "要上传的文件", required = true)
            @RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            FileUploadResponse response = fileService.uploadFile(file, clientIp);
            
            logger.info("文件上传成功: {} -> {}", file.getOriginalFilename(), response.getStoredName());
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", response));
        } catch (Exception e) {
            logger.error("文件上传失败: {}", file.getOriginalFilename(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 批量上传文件
     * 
     * @param files 要上传的文件数组，通过表单参数传递
     * @param request HTTP请求对象，用于获取客户端IP地址
     * @return 包含批量上传结果的响应实体，返回所有文件的上传信息列表
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传文件", description = "支持一次上传多个图片文件")
    public ResponseEntity<ApiResponse<List<FileUploadResponse>>> uploadFiles(
            @Parameter(description = "要上传的文件数组", required = true)
            @RequestParam("files") MultipartFile[] files,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            List<FileUploadResponse> responses = fileService.uploadFiles(files, clientIp);
            
            logger.info("批量文件上传成功，数量: {}", files.length);
            return ResponseEntity.ok(ApiResponse.success("批量文件上传成功", responses));
        } catch (Exception e) {
            logger.error("批量文件上传失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("批量文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 下载文件
     * 支持断点续传功能，通过Range请求头实现部分内容下载
     * 
     * @param fileName 要下载的文件名，从URL路径中获取
     * @param request HTTP请求对象，用于获取Range头信息
     * @param response HTTP响应对象，用于设置响应头
     * @return 包含文件资源的响应实体，支持完整下载和断点续传
     */
    @GetMapping("/download/{fileName}")
    @Operation(summary = "下载文件", description = "根据文件名下载文件，支持断点续传")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        try {
            // 记录下载
            fileService.recordDownload(fileName);
            
            Resource resource = fileService.downloadFile(fileName);
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            
            // 处理断点续传
            return handleRangeRequest(request, response, resource, fileInfo);
            
        } catch (Exception e) {
            logger.error("文件下载失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 在线预览文件
     * 主要用于图片文件的在线预览，设置合适的内容类型和缓存策略
     * 
     * @param fileName 要预览的文件名，从URL路径中获取
     * @return 包含文件资源的响应实体，设置了适当的媒体类型和缓存头
     */
    @GetMapping("/preview/{fileName}")
    @Operation(summary = "预览文件", description = "在线预览图片文件")
    public ResponseEntity<Resource> previewFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            Resource resource = fileService.previewFile(fileName);
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
            headers.setCacheControl("max-age=3600"); // 缓存1小时
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("文件预览失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取文件缩略图
     * 返回图片文件的缩略图版本，通常用于快速预览和减少带宽消耗
     * 
     * @param fileName 要获取缩略图的文件名，从URL路径中获取
     * @return 包含缩略图资源的响应实体，设置了JPEG格式和较长的缓存时间
     */
    @GetMapping("/thumbnail/{fileName}")
    @Operation(summary = "获取缩略图", description = "获取图片文件的缩略图")
    public ResponseEntity<Resource> getThumbnail(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            Resource resource = fileService.getThumbnail(fileName);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_JPEG);
            headers.setCacheControl("max-age=7200"); // 缓存2小时
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("获取缩略图失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取文件详细信息
     * 返回指定文件的元数据信息，包括文件大小、类型、上传时间等
     * 
     * @param fileName 要查询的文件名，从URL路径中获取
     * @return 包含文件信息的响应实体，成功时返回详细信息，文件不存在时返回404
     */
    @GetMapping("/info/{fileName}")
    @Operation(summary = "获取文件信息", description = "获取文件的详细信息")
    public ResponseEntity<ApiResponse<FileInfoResponse>> getFileInfo(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            FileInfoResponse response = fileService.getFileInfo(fileName);
            return ResponseEntity.ok(ApiResponse.success("获取文件信息成功", response));
        } catch (Exception e) {
            logger.error("获取文件信息失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("文件不存在: " + fileName));
        }
    }

    /**
     * 获取所有文件列表
     * 返回系统中所有已上传文件的信息列表，用于文件管理界面
     * 
     * @return 包含所有文件信息列表的响应实体
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "获取所有已上传的文件列表")
    public ResponseEntity<ApiResponse<List<FileInfoResponse>>> getFileList() {
        try {
            List<FileInfoResponse> responses = fileService.getAllFiles();
            return ResponseEntity.ok(ApiResponse.success("获取文件列表成功", responses));
        } catch (Exception e) {
            logger.error("获取文件列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取文件列表失败"));
        }
    }

    /**
     * 删除单个文件
     * 删除指定的文件及其相关的缩略图和数据库记录
     * 
     * @param fileName 要删除的文件名，从URL路径中获取
     * @return 删除操作的结果响应，成功时返回成功信息，失败时返回错误信息
     */
    @DeleteMapping("/{fileName}")
    @Operation(summary = "删除文件", description = "删除指定的文件")
    public ResponseEntity<ApiResponse<Object>> deleteFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            boolean success = fileService.deleteFile(fileName);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("文件删除成功"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error("文件删除失败"));
            }
        } catch (Exception e) {
            logger.error("删除文件失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("删除文件失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除文件
     * 一次性删除多个文件，提高删除效率
     * 
     * @param fileNames 要删除的文件名列表，通过请求体传递
     * @return 批量删除操作的结果响应
     */
    @PostMapping("/delete/batch")
    @Operation(summary = "批量删除文件", description = "批量删除多个文件")
    public ResponseEntity<ApiResponse<Object>> deleteFiles(
            @Parameter(description = "文件名列表", required = true)
            @RequestBody List<String> fileNames) {
        
        try {
            boolean success = fileService.deleteFiles(fileNames);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("批量删除文件成功"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error("批量删除文件失败"));
            }
        } catch (Exception e) {
            logger.error("批量删除文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("批量删除文件失败: " + e.getMessage()));
        }
    }

    /**
     * 获取存储统计信息
     * 返回文件存储的统计数据，包括总文件数量、总大小、可用空间等信息
     * 
     * @return 包含存储统计信息的响应实体
     */
    @GetMapping("/stats")
    @Operation(summary = "获取存储统计", description = "获取文件存储的统计信息")
    public ResponseEntity<ApiResponse<FileService.StorageStats>> getStorageStats() {
        try {
            FileService.StorageStats stats = fileService.getStorageStats();
            return ResponseEntity.ok(ApiResponse.success("获取存储统计成功", stats));
        } catch (Exception e) {
            logger.error("获取存储统计失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取存储统计失败"));
        }
    }

    /**
     * 获取客户端真实IP地址
     * 考虑代理服务器的情况，优先从代理头中获取真实IP
     * 
     * @param request HTTP请求对象
     * @return 客户端的真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 处理HTTP Range请求，实现断点续传功能
     * 支持客户端请求文件的部分内容，用于大文件下载的断点续传
     * 
     * @param request HTTP请求对象，包含Range头信息
     * @param response HTTP响应对象
     * @param resource 要下载的文件资源
     * @param fileInfo 文件的详细信息
     * @return 响应实体，包含完整文件或部分内容
     * @throws IOException 文件操作异常
     */
    private ResponseEntity<Resource> handleRangeRequest(HttpServletRequest request, 
                                                       HttpServletResponse response,
                                                       Resource resource, 
                                                       FileInfoResponse fileInfo) throws IOException {
        
        String range = request.getHeader("Range");
        long contentLength = fileInfo.getFileSize();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
        
        // 设置文件下载名称
        String encodedFileName = URLEncoder.encode(fileInfo.getOriginalName(), StandardCharsets.UTF_8);
        headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                   "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
        
        // 支持断点续传
        headers.add("Accept-Ranges", "bytes");
        
        if (range != null && range.startsWith("bytes=")) {
            // 处理范围请求
            String[] ranges = range.substring(6).split("-");
            long start = Long.parseLong(ranges[0]);
            long end = ranges.length > 1 && !ranges[1].isEmpty() ? 
                      Long.parseLong(ranges[1]) : contentLength - 1;
            
            long rangeLength = end - start + 1;
            
            headers.add("Content-Range", "bytes " + start + "-" + end + "/" + contentLength);
            headers.setContentLength(rangeLength);
            
            return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                    .headers(headers)
                    .body(resource);
        } else {
            // 完整文件下载
            headers.setContentLength(contentLength);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
        }
    }

    /**
     * 添加文字水印
     */
    @PostMapping("/watermark/text/{fileName}")
    @Operation(summary = "添加文字水印", description = "为图片添加文字水印")
    public ResponseEntity<ApiResponse<FileUploadResponse>> addTextWatermark(
            @PathVariable String fileName,
            @RequestParam String text,
            @RequestParam(defaultValue = "bottom-right") String position,
            @RequestParam(defaultValue = "0.5") float opacity,
            @RequestParam(defaultValue = "36") int fontSize,
            @RequestParam(defaultValue = "#FFFFFF") String fontColor,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            FileUploadResponse response = fileService.addTextWatermark(fileName, text, position, opacity, fontSize, fontColor, clientIp);
            
            logger.info("文字水印添加成功: {}, 文字: {}", fileName, text);
            return ResponseEntity.ok(ApiResponse.success("文字水印添加成功", response));
        } catch (Exception e) {
            logger.error("文字水印添加失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("文字水印添加失败: " + e.getMessage()));
        }
    }

    /**
     * 应用图片滤镜
     */
    @PostMapping("/filter/{fileName}")
    @Operation(summary = "应用图片滤镜", description = "为图片应用各种滤镜效果")
    public ResponseEntity<ApiResponse<FileUploadResponse>> applyFilter(
            @PathVariable String fileName,
            @RequestParam String filterType,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            FileUploadResponse response = fileService.applyFilter(fileName, filterType, clientIp);
            
            logger.info("滤镜应用成功: {}, 滤镜: {}", fileName, filterType);
            return ResponseEntity.ok(ApiResponse.success("滤镜应用成功", response));
        } catch (Exception e) {
            logger.error("滤镜应用失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("滤镜应用失败: " + e.getMessage()));
        }
    }

    /**
     * 图片格式转换
     */
    @PostMapping("/convert/{fileName}")
    @Operation(summary = "图片格式转换", description = "转换图片格式")
    public ResponseEntity<ApiResponse<FileUploadResponse>> convertFormat(
            @PathVariable String fileName,
            @RequestParam String targetFormat,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            FileUploadResponse response = fileService.convertFormat(fileName, targetFormat, clientIp);
            
            logger.info("格式转换成功: {}, 目标格式: {}", fileName, targetFormat);
            return ResponseEntity.ok(ApiResponse.success("格式转换成功", response));
        } catch (Exception e) {
            logger.error("格式转换失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("格式转换失败: " + e.getMessage()));
        }
    }

    /**
     * 图片旋转
     */
    @PostMapping("/rotate/{fileName}")
    @Operation(summary = "图片旋转", description = "旋转图片指定角度")
    public ResponseEntity<ApiResponse<FileUploadResponse>> rotateImage(
            @PathVariable String fileName,
            @RequestParam double angle,
            HttpServletRequest request) {
        
        try {
            String clientIp = getClientIpAddress(request);
            FileUploadResponse response = fileService.rotateImage(fileName, angle, clientIp);
            
            logger.info("图片旋转成功: {}, 角度: {}°", fileName, angle);
            return ResponseEntity.ok(ApiResponse.success("图片旋转成功", response));
        } catch (Exception e) {
            logger.error("图片旋转失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("图片旋转失败: " + e.getMessage()));
        }
    }

    /**
     * 生成二维码分享链接
     */
    @GetMapping("/qr/{fileName}")
    @Operation(summary = "生成二维码", description = "为图片生成二维码分享链接")
    public ResponseEntity<Resource> generateQRCode(@PathVariable String fileName) {
        try {
            Resource qrCodeResource = fileService.generateQRCode(fileName);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setCacheControl("max-age=3600");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(qrCodeResource);
                    
        } catch (Exception e) {
            logger.error("二维码生成失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 批量处理图片
     */
    @PostMapping("/batch/process")
    @Operation(summary = "批量处理图片", description = "批量应用滤镜、水印等操作")
    public ResponseEntity<ApiResponse<List<FileUploadResponse>>> batchProcess(
            @RequestBody BatchProcessRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String clientIp = getClientIpAddress(httpRequest);
            List<FileUploadResponse> responses = fileService.batchProcess(request, clientIp);
            
            logger.info("批量处理完成，处理文件数: {}", request.getFileNames().size());
            return ResponseEntity.ok(ApiResponse.success("批量处理完成", responses));
        } catch (Exception e) {
            logger.error("批量处理失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("批量处理失败: " + e.getMessage()));
        }
    }

    /**
     * 获取图片分析信息（AI功能）
     */
    @GetMapping("/analyze/{fileName}")
    @Operation(summary = "图片AI分析", description = "分析图片内容，提取标签和特征")
    public ResponseEntity<ApiResponse<AIAnalysisResult>> analyzeImage(@PathVariable String fileName) {
        try {
            // 获取文件信息
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/"; // 从配置获取
            File file = new File(uploadPath + fileInfo.getStoredName());

            // 异步执行AI分析
            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(file);
            AIAnalysisResult result = future.get(); // 等待结果

            logger.info("图片分析完成: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("图片分析完成", result));
        } catch (Exception e) {
            logger.error("图片分析失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("图片分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取色彩分析
     */
    @GetMapping("/colors/{fileName}")
    @Operation(summary = "色彩分析", description = "分析图片主要颜色")
    public ResponseEntity<ApiResponse<AIAnalysisResult.ColorAnalysis>> analyzeColors(@PathVariable String fileName) {
        try {
            // 获取文件路径
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/"; // 从配置获取
            File file = new File(uploadPath + fileInfo.getStoredName());

            // 异步执行颜色分析
            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeColors(file);
            AIAnalysisResult result = future.get();

            logger.info("色彩分析完成: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("色彩分析完成", result.getColorAnalysis()));
        } catch (Exception e) {
            logger.error("色彩分析失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("色彩分析失败: " + e.getMessage()));
        }
    }

    /**
     * 物体检测
     */
    @GetMapping("/detect/objects/{fileName}")
    @Operation(summary = "物体检测", description = "检测图片中的物体")
    public ResponseEntity<ApiResponse<List<AIAnalysisResult.DetectedObject>>> detectObjects(@PathVariable String fileName) {
        try {
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/";
            File file = new File(uploadPath + fileInfo.getStoredName());

            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectObjects(file);
            AIAnalysisResult result = future.get();

            logger.info("物体检测完成: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("物体检测完成", result.getObjects()));
        } catch (Exception e) {
            logger.error("物体检测失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("物体检测失败: " + e.getMessage()));
        }
    }

    /**
     * 人脸检测
     */
    @GetMapping("/detect/faces/{fileName}")
    @Operation(summary = "人脸检测", description = "检测图片中的人脸")
    public ResponseEntity<ApiResponse<List<AIAnalysisResult.Face>>> detectFaces(@PathVariable String fileName) {
        try {
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/";
            File file = new File(uploadPath + fileInfo.getStoredName());

            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectFaces(file);
            AIAnalysisResult result = future.get();

            logger.info("人脸检测完成: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("人脸检测完成", result.getFaces()));
        } catch (Exception e) {
            logger.error("人脸检测失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("人脸检测失败: " + e.getMessage()));
        }
    }

    /**
     * 文字识别
     */
    @GetMapping("/recognize/text/{fileName}")
    @Operation(summary = "文字识别", description = "识别图片中的文字")
    public ResponseEntity<ApiResponse<List<AIAnalysisResult.TextDetection>>> recognizeText(@PathVariable String fileName) {
        try {
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/";
            File file = new File(uploadPath + fileInfo.getStoredName());

            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.recognizeText(file);
            AIAnalysisResult result = future.get();

            logger.info("文字识别完成: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("文字识别完成", result.getTextDetections()));
        } catch (Exception e) {
            logger.error("文字识别失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("文字识别失败: " + e.getMessage()));
        }
    }

    /**
     * 异步AI分析（支持WebSocket通知）
     */
    @PostMapping("/analyze/async/{fileName}")
    @Operation(summary = "异步AI分析", description = "异步执行AI分析，通过WebSocket推送结果")
    public ResponseEntity<ApiResponse<String>> analyzeImageAsync(@PathVariable String fileName) {
        try {
            FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
            String uploadPath = "./uploads/";
            File file = new File(uploadPath + fileInfo.getStoredName());

            // 异步执行分析，不等待结果
            aiImageAnalysisService.analyzeImage(file);

            logger.info("异步AI分析已启动: {}", fileName);
            return ResponseEntity.ok(ApiResponse.success("AI分析已启动，结果将通过WebSocket推送", "分析中..."));
        } catch (Exception e) {
            logger.error("启动异步AI分析失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("启动AI分析失败: " + e.getMessage()));
        }
    }

    /**
     * 批量AI分析
     */
    @PostMapping("/analyze/batch")
    @Operation(summary = "批量AI分析", description = "批量分析多个图片")
    public ResponseEntity<ApiResponse<String>> batchAnalyze(@RequestBody List<String> fileNames) {
        try {
            String batchId = "batch_" + System.currentTimeMillis();

            // 发送批量处理开始通知
            notificationService.sendBatchProcessStart(batchId, fileNames.size(), "AI分析");

            // 异步处理每个文件
            CompletableFuture.runAsync(() -> {
                int processed = 0;
                String uploadPath = "./uploads/";

                for (String fileName : fileNames) {
                    try {
                        FileInfoResponse fileInfo = fileService.getFileInfo(fileName);
                        File file = new File(uploadPath + fileInfo.getStoredName());

                        // 发送进度通知
                        notificationService.sendBatchProcessProgress(batchId, fileNames.size(), processed, fileName, "AI分析");

                        // 执行分析
                        aiImageAnalysisService.analyzeImage(file).get();
                        processed++;

                    } catch (Exception e) {
                        logger.error("批量分析文件失败: {}", fileName, e);
                    }
                }

                // 发送完成通知
                notificationService.sendBatchProcessComplete(batchId, fileNames.size(), "AI分析");
            });

            logger.info("批量AI分析已启动，文件数量: {}", fileNames.size());
            return ResponseEntity.ok(ApiResponse.success("批量AI分析已启动", batchId));
        } catch (Exception e) {
            logger.error("启动批量AI分析失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.badRequest("启动批量分析失败: " + e.getMessage()));
        }
    }
}