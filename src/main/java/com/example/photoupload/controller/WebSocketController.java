package com.example.photoupload.controller;

import com.example.photoupload.dto.WebSocketMessage;
import com.example.photoupload.service.WebSocketNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket消息控制器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class WebSocketController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);
    
    @Autowired
    private WebSocketNotificationService notificationService;

    /**
     * 处理客户端发送的心跳消息
     */
    @MessageMapping("/heartbeat")
    @SendTo("/topic/heartbeat")
    public Map<String, Object> handleHeartbeat(@Payload Map<String, Object> message, 
                                              SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        
        Map<String, Object> response = new HashMap<>();
        response.put("type", "heartbeat_response");
        response.put("timestamp", System.currentTimeMillis());
        response.put("session_id", sessionId);
        response.put("online_users", notificationService.getOnlineUserCount());
        response.put("active_sessions", notificationService.getActiveSessionCount());
        
        logger.debug("处理心跳消息: sessionId={}", sessionId);
        
        return response;
    }

    /**
     * 处理客户端发送的聊天消息
     */
    @MessageMapping("/chat")
    @SendTo("/topic/chat")
    public WebSocketMessage handleChatMessage(@Payload Map<String, Object> message, 
                                            SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        
        String content = (String) message.get("content");
        String messageType = (String) message.getOrDefault("messageType", "text");
        
        Map<String, Object> chatData = new HashMap<>();
        chatData.put("user_id", userId);
        chatData.put("content", content);
        chatData.put("message_type", messageType);
        chatData.put("session_id", sessionId);
        
        WebSocketMessage wsMessage = new WebSocketMessage(
            WebSocketMessage.MessageType.USER_NOTIFICATION, 
            chatData, 
            sessionId, 
            userId
        );
        
        logger.info("处理聊天消息: userId={}, content={}", userId, content);
        
        return wsMessage;
    }

    /**
     * 处理客户端状态更新
     */
    @MessageMapping("/status")
    public void handleStatusUpdate(@Payload Map<String, Object> message, 
                                  SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        
        String status = (String) message.get("status");
        String activity = (String) message.get("activity");
        
        logger.debug("用户状态更新: userId={}, status={}, activity={}", userId, status, activity);
        
        // 可以在这里更新用户状态到数据库或缓存
        // 然后广播状态变更给其他用户
        
        Map<String, Object> statusData = new HashMap<>();
        statusData.put("user_id", userId);
        statusData.put("status", status);
        statusData.put("activity", activity);
        
        WebSocketMessage statusMessage = new WebSocketMessage(
            WebSocketMessage.MessageType.USER_NOTIFICATION, 
            statusData, 
            sessionId, 
            userId
        );
        
        notificationService.broadcast(statusMessage);
    }

    /**
     * 处理文件操作请求
     */
    @MessageMapping("/file-operation")
    public void handleFileOperation(@Payload Map<String, Object> message, 
                                   SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        
        String operation = (String) message.get("operation");
        String fileName = (String) message.get("fileName");
        
        logger.info("处理文件操作请求: userId={}, operation={}, fileName={}", userId, operation, fileName);
        
        switch (operation) {
            case "like":
                notificationService.sendFileLiked(fileName, userId);
                break;
            case "share":
                String shareUrl = generateShareUrl(fileName);
                notificationService.sendFileShared(fileName, userId, shareUrl);
                break;
            case "comment":
                String comment = (String) message.get("comment");
                notificationService.sendFileCommented(fileName, userId, comment);
                break;
            default:
                logger.warn("未知的文件操作: {}", operation);
                break;
        }
    }

    /**
     * 处理系统命令
     */
    @MessageMapping("/system-command")
    public void handleSystemCommand(@Payload Map<String, Object> message, 
                                   SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        
        String command = (String) message.get("command");
        
        logger.info("处理系统命令: userId={}, command={}", userId, command);
        
        switch (command) {
            case "get-stats":
                sendSystemStats(sessionId);
                break;
            case "clear-notifications":
                clearUserNotifications(userId);
                break;
            case "refresh-data":
                refreshUserData(userId);
                break;
            default:
                logger.warn("未知的系统命令: {}", command);
                break;
        }
    }

    /**
     * 发送系统统计信息
     */
    private void sendSystemStats(String sessionId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("online_users", notificationService.getOnlineUserCount());
        stats.put("active_sessions", notificationService.getActiveSessionCount());
        stats.put("server_time", System.currentTimeMillis());
        stats.put("uptime", getServerUptime());
        
        WebSocketMessage message = new WebSocketMessage(
            WebSocketMessage.MessageType.SYSTEM_NOTIFICATION, 
            stats
        );
        message.setSessionId(sessionId);
        
        notificationService.sendToSession(sessionId, message);
    }

    /**
     * 清除用户通知
     */
    private void clearUserNotifications(String userId) {
        notificationService.sendUserNotification(
            userId, 
            "通知已清除", 
            "所有通知已被清除", 
            WebSocketMessage.NotificationData.NotificationLevel.INFO
        );
    }

    /**
     * 刷新用户数据
     */
    private void refreshUserData(String userId) {
        notificationService.sendUserNotification(
            userId, 
            "数据已刷新", 
            "用户数据已刷新", 
            WebSocketMessage.NotificationData.NotificationLevel.SUCCESS
        );
    }

    /**
     * 生成分享链接
     */
    private String generateShareUrl(String fileName) {
        // 这里应该生成真实的分享链接
        return "http://localhost:8080/api/files/preview/" + fileName + "?share=true";
    }

    /**
     * 获取服务器运行时间
     */
    private long getServerUptime() {
        // 这里应该返回真实的服务器运行时间
        return System.currentTimeMillis();
    }
}
