package com.example.photoupload;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot 照片上传下载系统主启动类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
public class PhotoUploadApplication {

    public static void main(String[] args) {
        SpringApplication.run(PhotoUploadApplication.class, args);
    }
} 