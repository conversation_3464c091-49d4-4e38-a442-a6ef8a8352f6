package com.example.photoupload.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.tika.Tika;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 文件工具类
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileUtils {

    private static final Tika tika = new Tika();

    /**
     * 生成唯一文件名
     */
    public static String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        return timestamp + "_" + uuid + "." + extension;
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 获取文件名（不含扩展名）
     */
    public static String getFileNameWithoutExtension(String filename) {
        if (filename == null) {
            return "";
        }
        int dotIndex = filename.lastIndexOf(".");
        return dotIndex == -1 ? filename : filename.substring(0, dotIndex);
    }

    /**
     * 安全化文件名（移除危险字符）
     */
    public static String sanitizeFileName(String filename) {
        if (filename == null) {
            return "";
        }
        // 移除或替换危险字符
        return filename.replaceAll("[^a-zA-Z0-9\\.\\-_]", "_");
    }

    /**
     * 创建目录
     */
    public static void createDirectories(String path) throws IOException {
        Path dirPath = Paths.get(path);
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }
    }

    /**
     * 计算文件MD5哈希值
     */
    public static String calculateMD5(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return DigestUtils.md5Hex(inputStream);
        }
    }

    /**
     * 计算文件MD5哈希值
     */
    public static String calculateMD5(File file) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(file)) {
            return DigestUtils.md5Hex(inputStream);
        }
    }

    /**
     * 检测文件真实类型
     */
    public static String detectContentType(File file) throws IOException {
        return tika.detect(file);
    }

    /**
     * 检测文件真实类型
     */
    public static String detectContentType(MultipartFile file) throws IOException {
        return tika.detect(file.getInputStream(), file.getOriginalFilename());
    }

    /**
     * 检查是否为图片文件
     */
    public static boolean isImageFile(String contentType) {
        return contentType != null && contentType.startsWith("image/");
    }

    /**
     * 获取图片尺寸
     */
    public static int[] getImageDimensions(File imageFile) throws IOException {
        BufferedImage image = ImageIO.read(imageFile);
        if (image != null) {
            return new int[]{image.getWidth(), image.getHeight()};
        }
        return new int[]{0, 0};
    }

    /**
     * 获取图片尺寸
     */
    public static int[] getImageDimensions(InputStream inputStream) throws IOException {
        BufferedImage image = ImageIO.read(inputStream);
        if (image != null) {
            return new int[]{image.getWidth(), image.getHeight()};
        }
        return new int[]{0, 0};
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 复制文件
     */
    public static void copyFile(File source, File target) throws IOException {
        try (FileInputStream in = new FileInputStream(source);
             FileOutputStream out = new FileOutputStream(target)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
    }

    /**
     * 删除文件
     */
    public static boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            return Files.deleteIfExists(path);
        } catch (IOException e) {
            return false;
        }
    }
} 