package com.example.photoupload.service.impl;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.dto.WebSocketMessage;
import com.example.photoupload.service.WebSocketNotificationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket通知服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class WebSocketNotificationServiceImpl implements WebSocketNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketNotificationServiceImpl.class);
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // 存储活跃的会话信息
    private final Map<String, String> sessionUserMap = new ConcurrentHashMap<>();
    private final Map<String, String> userSessionMap = new ConcurrentHashMap<>();

    @Override
    public void sendUploadProgress(String sessionId, String fileName, Integer progress, 
                                  Long bytesUploaded, Long totalBytes, String speed) {
        WebSocketMessage.UploadProgressData data = new WebSocketMessage.UploadProgressData(
            fileName, progress, bytesUploaded, totalBytes, speed);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.UPLOAD_PROGRESS, data);
        message.setSessionId(sessionId);
        
        sendToSession(sessionId, message);
        
        logger.debug("发送上传进度通知: {} - {}%", fileName, progress);
    }

    @Override
    public void sendUploadComplete(String sessionId, String fileName) {
        Map<String, Object> data = new HashMap<>();
        data.put("file_name", fileName);
        data.put("message", "文件上传完成");
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.UPLOAD_COMPLETE, data);
        message.setSessionId(sessionId);
        
        sendToSession(sessionId, message);
        
        logger.info("发送上传完成通知: {}", fileName);
    }

    @Override
    public void sendUploadError(String sessionId, String fileName, String errorMessage) {
        Map<String, Object> data = new HashMap<>();
        data.put("file_name", fileName);
        data.put("error_message", errorMessage);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.UPLOAD_ERROR, data);
        message.setSessionId(sessionId);
        
        sendToSession(sessionId, message);
        
        logger.warn("发送上传错误通知: {} - {}", fileName, errorMessage);
    }

    @Override
    public void sendAIAnalysisStart(String fileName) {
        WebSocketMessage.AIAnalysisData data = new WebSocketMessage.AIAnalysisData(fileName, "comprehensive", 0);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.AI_ANALYSIS_START, data);
        
        broadcast(message);
        
        logger.info("发送AI分析开始通知: {}", fileName);
    }

    @Override
    public void sendAIAnalysisComplete(String fileName, AIAnalysisResult result) {
        WebSocketMessage.AIAnalysisData data = new WebSocketMessage.AIAnalysisData(fileName, "comprehensive", 100);
        data.setResult(result);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.AI_ANALYSIS_COMPLETE, data);
        
        broadcast(message);
        
        logger.info("发送AI分析完成通知: {}", fileName);
    }

    @Override
    public void sendAIAnalysisError(String fileName, String errorMessage) {
        WebSocketMessage.AIAnalysisData data = new WebSocketMessage.AIAnalysisData(fileName, "comprehensive", 0);
        data.setErrorMessage(errorMessage);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.AI_ANALYSIS_ERROR, data);
        
        broadcast(message);
        
        logger.warn("发送AI分析错误通知: {} - {}", fileName, errorMessage);
    }

    @Override
    public void sendSystemNotification(String title, String message, WebSocketMessage.NotificationData.NotificationLevel level) {
        WebSocketMessage.NotificationData data = new WebSocketMessage.NotificationData(title, message, level);
        
        WebSocketMessage wsMessage = new WebSocketMessage(WebSocketMessage.MessageType.SYSTEM_NOTIFICATION, data);
        
        broadcast(wsMessage);
        
        logger.info("发送系统通知: {} - {}", title, message);
    }

    @Override
    public void sendUserNotification(String userId, String title, String message, 
                                   WebSocketMessage.NotificationData.NotificationLevel level) {
        WebSocketMessage.NotificationData data = new WebSocketMessage.NotificationData(title, message, level);
        
        WebSocketMessage wsMessage = new WebSocketMessage(WebSocketMessage.MessageType.USER_NOTIFICATION, data);
        wsMessage.setUserId(userId);
        
        sendToUser(userId, wsMessage);
        
        logger.info("发送用户通知: {} - {} - {}", userId, title, message);
    }

    @Override
    public void sendBatchProcessStart(String batchId, Integer totalFiles, String operation) {
        WebSocketMessage.BatchProcessData data = new WebSocketMessage.BatchProcessData(batchId, totalFiles, 0, operation);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.BATCH_PROCESS_START, data);
        
        broadcast(message);
        
        logger.info("发送批量处理开始通知: {} - {} files", batchId, totalFiles);
    }

    @Override
    public void sendBatchProcessProgress(String batchId, Integer totalFiles, Integer processedFiles, 
                                       String currentFile, String operation) {
        WebSocketMessage.BatchProcessData data = new WebSocketMessage.BatchProcessData(batchId, totalFiles, processedFiles, operation);
        data.setCurrentFile(currentFile);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.BATCH_PROCESS_PROGRESS, data);
        
        broadcast(message);
        
        logger.debug("发送批量处理进度通知: {} - {}/{}", batchId, processedFiles, totalFiles);
    }

    @Override
    public void sendBatchProcessComplete(String batchId, Integer totalFiles, String operation) {
        WebSocketMessage.BatchProcessData data = new WebSocketMessage.BatchProcessData(batchId, totalFiles, totalFiles, operation);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.BATCH_PROCESS_COMPLETE, data);
        
        broadcast(message);
        
        logger.info("发送批量处理完成通知: {} - {} files", batchId, totalFiles);
    }

    @Override
    public void sendFileShared(String fileName, String sharedBy, String shareUrl) {
        Map<String, Object> data = new HashMap<>();
        data.put("file_name", fileName);
        data.put("shared_by", sharedBy);
        data.put("share_url", shareUrl);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.FILE_SHARED, data);
        
        broadcast(message);
        
        logger.info("发送文件分享通知: {} by {}", fileName, sharedBy);
    }

    @Override
    public void sendFileLiked(String fileName, String likedBy) {
        Map<String, Object> data = new HashMap<>();
        data.put("file_name", fileName);
        data.put("liked_by", likedBy);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.FILE_LIKED, data);
        
        broadcast(message);
        
        logger.info("发送文件点赞通知: {} by {}", fileName, likedBy);
    }

    @Override
    public void sendFileCommented(String fileName, String commentedBy, String comment) {
        Map<String, Object> data = new HashMap<>();
        data.put("file_name", fileName);
        data.put("commented_by", commentedBy);
        data.put("comment", comment);
        
        WebSocketMessage message = new WebSocketMessage(WebSocketMessage.MessageType.FILE_COMMENTED, data);
        
        broadcast(message);
        
        logger.info("发送文件评论通知: {} by {} - {}", fileName, commentedBy, comment);
    }

    @Override
    public void sendToSession(String sessionId, WebSocketMessage message) {
        try {
            messagingTemplate.convertAndSendToUser(sessionId, "/queue/notifications", message);
            logger.debug("向会话发送消息: {} - {}", sessionId, message.getType());
        } catch (Exception e) {
            logger.error("发送会话消息失败: {}", sessionId, e);
        }
    }

    @Override
    public void sendToUser(String userId, WebSocketMessage message) {
        try {
            String sessionId = userSessionMap.get(userId);
            if (sessionId != null) {
                sendToSession(sessionId, message);
            } else {
                logger.warn("用户 {} 不在线，无法发送消息", userId);
            }
        } catch (Exception e) {
            logger.error("发送用户消息失败: {}", userId, e);
        }
    }

    @Override
    public void broadcast(WebSocketMessage message) {
        try {
            messagingTemplate.convertAndSend("/topic/notifications", message);
            logger.debug("广播消息: {}", message.getType());
        } catch (Exception e) {
            logger.error("广播消息失败", e);
        }
    }

    @Override
    public int getOnlineUserCount() {
        return userSessionMap.size();
    }

    @Override
    public int getActiveSessionCount() {
        return sessionUserMap.size();
    }

    /**
     * 注册用户会话
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     */
    public void registerUserSession(String sessionId, String userId) {
        sessionUserMap.put(sessionId, userId);
        userSessionMap.put(userId, sessionId);
        
        logger.info("用户会话注册: {} -> {}", userId, sessionId);
        
        // 发送欢迎通知
        sendUserNotification(userId, "欢迎", "您已成功连接到实时通知系统", 
                           WebSocketMessage.NotificationData.NotificationLevel.INFO);
    }

    /**
     * 注销用户会话
     * 
     * @param sessionId 会话ID
     */
    public void unregisterUserSession(String sessionId) {
        String userId = sessionUserMap.remove(sessionId);
        if (userId != null) {
            userSessionMap.remove(userId);
            logger.info("用户会话注销: {} -> {}", userId, sessionId);
        }
    }

    /**
     * 获取会话对应的用户ID
     * 
     * @param sessionId 会话ID
     * @return 用户ID
     */
    public String getUserIdBySession(String sessionId) {
        return sessionUserMap.get(sessionId);
    }

    /**
     * 获取用户对应的会话ID
     * 
     * @param userId 用户ID
     * @return 会话ID
     */
    public String getSessionIdByUser(String userId) {
        return userSessionMap.get(userId);
    }
}
