package com.example.photoupload.service.impl;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.service.ImageProcessingService;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.awt.geom.AffineTransform;
import java.awt.font.FontRenderContext;
import java.awt.font.TextLayout;

/**
 * 图片处理服务实现类 - 增强版
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Service
public class ImageProcessingServiceImpl implements ImageProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(ImageProcessingServiceImpl.class);

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Value("${file.upload.compress.enabled:true}")
    private boolean compressEnabled;

    @Value("${file.upload.compress.quality:0.8}")
    private double compressQuality;

    @Value("${file.upload.compress.max-width:1920}")
    private int maxWidth;

    @Value("${file.upload.compress.max-height:1080}")
    private int maxHeight;

    @Value("${file.upload.thumbnail.enabled:true}")
    private boolean thumbnailEnabled;

    @Value("${file.upload.thumbnail.width:200}")
    private int thumbnailWidth;

    @Value("${file.upload.thumbnail.height:200}")
    private int thumbnailHeight;

    @Override
    public File compressImage(File imageFile) throws IOException {
        try {
            String parentPath = imageFile.getParent();
            String fileName = imageFile.getName();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            
            File compressedFile = new File(parentPath, nameWithoutExt + "_compressed" + extension);

            FileUploadConfig.Compress compressConfig = fileUploadConfig.getCompress();
            
            Thumbnails.of(imageFile)
                    .size(compressConfig.getMaxWidth(), compressConfig.getMaxHeight())
                    .outputQuality(compressConfig.getQuality())
                    .toFile(compressedFile);

            logger.info("图片压缩成功: {} -> {}", imageFile.getName(), compressedFile.getName());
            return compressedFile;
        } catch (Exception e) {
            logger.error("图片压缩失败: {}", imageFile.getName(), e);
            throw new IOException("图片压缩失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File generateThumbnail(File imageFile) throws IOException {
        try {
            String parentPath = imageFile.getParent();
            String fileName = imageFile.getName();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            
            File thumbnailFile = new File(parentPath, nameWithoutExt + fileUploadConfig.getThumbnail().getSuffix() + extension);

            FileUploadConfig.Thumbnail thumbnailConfig = fileUploadConfig.getThumbnail();
            
            Thumbnails.of(imageFile)
                    .size(thumbnailConfig.getWidth(), thumbnailConfig.getHeight())
                    .outputQuality(0.8)
                    .toFile(thumbnailFile);

            logger.info("缩略图生成成功: {} -> {}", imageFile.getName(), thumbnailFile.getName());
            return thumbnailFile;
        } catch (Exception e) {
            logger.error("缩略图生成失败: {}", imageFile.getName(), e);
            throw new IOException("缩略图生成失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File resizeImage(File imageFile, int width, int height) throws IOException {
        try {
            String parentPath = imageFile.getParent();
            String fileName = imageFile.getName();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            
            File resizedFile = new File(parentPath, nameWithoutExt + "_resized_" + width + "x" + height + extension);

            Thumbnails.of(imageFile)
                    .size(width, height)
                    .toFile(resizedFile);

            logger.info("图片调整大小成功: {} -> {}", imageFile.getName(), resizedFile.getName());
            return resizedFile;
        } catch (Exception e) {
            logger.error("图片调整大小失败: {}", imageFile.getName(), e);
            throw new IOException("图片调整大小失败: " + e.getMessage(), e);
        }
    }

    @Override
    public File cropImage(File imageFile, int x, int y, int width, int height) throws IOException {
        try {
            String parentPath = imageFile.getParent();
            String fileName = imageFile.getName();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            
            File croppedFile = new File(parentPath, nameWithoutExt + "_cropped" + extension);

            Thumbnails.of(imageFile)
                    .sourceRegion(x, y, width, height)
                    .size(width, height)
                    .toFile(croppedFile);

            logger.info("图片裁剪成功: {} -> {}", imageFile.getName(), croppedFile.getName());
            return croppedFile;
        } catch (Exception e) {
            logger.error("图片裁剪失败: {}", imageFile.getName(), e);
            throw new IOException("图片裁剪失败: " + e.getMessage(), e);
        }
    }

    public boolean compressImage(String inputPath, String outputPath) {
        if (!compressEnabled) {
            return false;
        }

        try {
            File inputFile = new File(inputPath);
            File outputFile = new File(outputPath);

            BufferedImage originalImage = ImageIO.read(inputFile);
            if (originalImage == null) {
                logger.warn("无法读取图片文件: {}", inputPath);
                return false;
            }

            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 计算新的尺寸
            double scaleFactor = Math.min(
                (double) maxWidth / originalWidth,
                (double) maxHeight / originalHeight
            );

            if (scaleFactor >= 1.0) {
                // 不需要压缩尺寸，只压缩质量
                Thumbnails.of(inputFile)
                    .scale(1.0)
                    .outputQuality(compressQuality)
                    .toFile(outputFile);
            } else {
                // 需要压缩尺寸和质量
                Thumbnails.of(inputFile)
                    .scale(scaleFactor)
                    .outputQuality(compressQuality)
                    .toFile(outputFile);
            }

            logger.info("图片压缩成功: {} -> {}, 压缩比: {:.2f}", 
                inputPath, outputPath, scaleFactor);
            return true;

        } catch (IOException e) {
            logger.error("图片压缩失败: {}", inputPath, e);
            return false;
        }
    }

    public boolean generateThumbnail(String inputPath, String outputPath) {
        if (!thumbnailEnabled) {
            return false;
        }

        try {
            File inputFile = new File(inputPath);
            File outputFile = new File(outputPath);

            Thumbnails.of(inputFile)
                .size(thumbnailWidth, thumbnailHeight)
                .outputQuality(0.9)
                .toFile(outputFile);

            logger.info("缩略图生成成功: {} -> {}", inputPath, outputPath);
            return true;

        } catch (IOException e) {
            logger.error("缩略图生成失败: {}", inputPath, e);
            return false;
        }
    }

    public BufferedImage getImageInfo(String imagePath) {
        try {
            return ImageIO.read(new File(imagePath));
        } catch (IOException e) {
            logger.error("获取图片信息失败: {}", imagePath, e);
            return null;
        }
    }

    /**
     * 添加文字水印
     */
    public boolean addTextWatermark(String inputPath, String outputPath, 
                                   String watermarkText, String position, 
                                   float opacity, int fontSize, String fontColor) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(inputPath));
            if (originalImage == null) {
                return false;
            }

            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            // 创建新的图像，支持透明度
            BufferedImage watermarkedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = watermarkedImage.createGraphics();

            // 绘制原始图像
            g2d.drawImage(originalImage, 0, 0, null);

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 设置字体
            Font font = new Font("Microsoft YaHei", Font.BOLD, fontSize);
            g2d.setFont(font);

            // 设置颜色和透明度
            Color color = Color.decode(fontColor);
            g2d.setColor(new Color(color.getRed(), color.getGreen(), color.getBlue(), (int)(opacity * 255)));

            // 获取文字尺寸
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(watermarkText);
            int textHeight = fm.getHeight();

            // 计算水印位置
            int x, y;
            switch (position.toLowerCase()) {
                case "top-left":
                    x = 20;
                    y = textHeight + 20;
                    break;
                case "top-right":
                    x = width - textWidth - 20;
                    y = textHeight + 20;
                    break;
                case "bottom-left":
                    x = 20;
                    y = height - 20;
                    break;
                case "bottom-right":
                    x = width - textWidth - 20;
                    y = height - 20;
                    break;
                case "center":
                default:
                    x = (width - textWidth) / 2;
                    y = (height + textHeight) / 2;
                    break;
            }

            // 绘制水印文字
            g2d.drawString(watermarkText, x, y);
            g2d.dispose();

            // 保存图像
            ImageIO.write(watermarkedImage, "PNG", new File(outputPath));
            
            logger.info("文字水印添加成功: {} -> {}", inputPath, outputPath);
            return true;

        } catch (IOException e) {
            logger.error("添加文字水印失败: {}", inputPath, e);
            return false;
        }
    }

    /**
     * 添加图片水印
     */
    public boolean addImageWatermark(String inputPath, String outputPath, 
                                   String watermarkImagePath, String position, 
                                   float opacity, float scale) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(inputPath));
            BufferedImage watermarkImage = ImageIO.read(new File(watermarkImagePath));
            
            if (originalImage == null || watermarkImage == null) {
                return false;
            }

            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            // 缩放水印图片
            int watermarkWidth = (int) (watermarkImage.getWidth() * scale);
            int watermarkHeight = (int) (watermarkImage.getHeight() * scale);
            
            BufferedImage scaledWatermark = new BufferedImage(watermarkWidth, watermarkHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = scaledWatermark.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(watermarkImage, 0, 0, watermarkWidth, watermarkHeight, null);
            g2d.dispose();

            // 创建结果图像
            BufferedImage watermarkedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            g2d = watermarkedImage.createGraphics();

            // 绘制原始图像
            g2d.drawImage(originalImage, 0, 0, null);

            // 设置透明度
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, opacity));

            // 计算水印位置
            int x, y;
            switch (position.toLowerCase()) {
                case "top-left":
                    x = 20;
                    y = 20;
                    break;
                case "top-right":
                    x = width - watermarkWidth - 20;
                    y = 20;
                    break;
                case "bottom-left":
                    x = 20;
                    y = height - watermarkHeight - 20;
                    break;
                case "bottom-right":
                    x = width - watermarkWidth - 20;
                    y = height - watermarkHeight - 20;
                    break;
                case "center":
                default:
                    x = (width - watermarkWidth) / 2;
                    y = (height - watermarkHeight) / 2;
                    break;
            }

            // 绘制水印
            g2d.drawImage(scaledWatermark, x, y, null);
            g2d.dispose();

            // 保存图像
            ImageIO.write(watermarkedImage, "JPG", new File(outputPath));
            
            logger.info("图片水印添加成功: {} -> {}", inputPath, outputPath);
            return true;

        } catch (IOException e) {
            logger.error("添加图片水印失败: {}", inputPath, e);
            return false;
        }
    }

    /**
     * 应用图片滤镜
     */
    public boolean applyFilter(String inputPath, String outputPath, String filterType) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(inputPath));
            if (originalImage == null) {
                return false;
            }

            BufferedImage filteredImage = null;

            switch (filterType.toLowerCase()) {
                case "grayscale":
                    filteredImage = applyGrayscaleFilter(originalImage);
                    break;
                case "sepia":
                    filteredImage = applySepiaFilter(originalImage);
                    break;
                case "vintage":
                    filteredImage = applyVintageFilter(originalImage);
                    break;
                case "blur":
                    filteredImage = applyBlurFilter(originalImage);
                    break;
                case "sharpen":
                    filteredImage = applySharpenFilter(originalImage);
                    break;
                case "emboss":
                    filteredImage = applyEmbossFilter(originalImage);
                    break;
                default:
                    filteredImage = originalImage;
                    break;
            }

            if (filteredImage != null) {
                ImageIO.write(filteredImage, "JPG", new File(outputPath));
                logger.info("滤镜应用成功: {} -> {}, 滤镜: {}", inputPath, outputPath, filterType);
                return true;
            }

        } catch (IOException e) {
            logger.error("应用滤镜失败: {}", inputPath, e);
        }
        return false;
    }

    /**
     * 黑白滤镜
     */
    private BufferedImage applyGrayscaleFilter(BufferedImage original) {
        BufferedImage result = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < original.getWidth(); x++) {
            for (int y = 0; y < original.getHeight(); y++) {
                Color color = new Color(original.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                Color grayColor = new Color(gray, gray, gray);
                result.setRGB(x, y, grayColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 复古滤镜
     */
    private BufferedImage applySepiaFilter(BufferedImage original) {
        BufferedImage result = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < original.getWidth(); x++) {
            for (int y = 0; y < original.getHeight(); y++) {
                Color color = new Color(original.getRGB(x, y));
                
                int r = color.getRed();
                int g = color.getGreen();
                int b = color.getBlue();
                
                int newR = Math.min(255, (int) (0.393 * r + 0.769 * g + 0.189 * b));
                int newG = Math.min(255, (int) (0.349 * r + 0.686 * g + 0.168 * b));
                int newB = Math.min(255, (int) (0.272 * r + 0.534 * g + 0.131 * b));
                
                Color sepiaColor = new Color(newR, newG, newB);
                result.setRGB(x, y, sepiaColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 怀旧滤镜
     */
    private BufferedImage applyVintageFilter(BufferedImage original) {
        BufferedImage result = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < original.getWidth(); x++) {
            for (int y = 0; y < original.getHeight(); y++) {
                Color color = new Color(original.getRGB(x, y));
                
                // 增加对比度和饱和度，减少蓝色
                int r = Math.min(255, (int) (color.getRed() * 1.2));
                int g = Math.min(255, (int) (color.getGreen() * 1.1));
                int b = Math.max(0, (int) (color.getBlue() * 0.8));
                
                Color vintageColor = new Color(r, g, b);
                result.setRGB(x, y, vintageColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 模糊滤镜
     */
    private BufferedImage applyBlurFilter(BufferedImage original) {
        int width = original.getWidth();
        int height = original.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        int radius = 3;
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                int r = 0, g = 0, b = 0, count = 0;
                
                for (int dx = -radius; dx <= radius; dx++) {
                    for (int dy = -radius; dy <= radius; dy++) {
                        int nx = Math.max(0, Math.min(width - 1, x + dx));
                        int ny = Math.max(0, Math.min(height - 1, y + dy));
                        
                        Color color = new Color(original.getRGB(nx, ny));
                        r += color.getRed();
                        g += color.getGreen();
                        b += color.getBlue();
                        count++;
                    }
                }
                
                Color blurColor = new Color(r / count, g / count, b / count);
                result.setRGB(x, y, blurColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 锐化滤镜
     */
    private BufferedImage applySharpenFilter(BufferedImage original) {
        int width = original.getWidth();
        int height = original.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        // 锐化卷积核
        float[] sharpenKernel = {
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        };
        
        for (int x = 1; x < width - 1; x++) {
            for (int y = 1; y < height - 1; y++) {
                float r = 0, g = 0, b = 0;
                
                for (int dx = -1; dx <= 1; dx++) {
                    for (int dy = -1; dy <= 1; dy++) {
                        Color color = new Color(original.getRGB(x + dx, y + dy));
                        float factor = sharpenKernel[(dy + 1) * 3 + (dx + 1)];
                        
                        r += color.getRed() * factor;
                        g += color.getGreen() * factor;
                        b += color.getBlue() * factor;
                    }
                }
                
                r = Math.max(0, Math.min(255, r));
                g = Math.max(0, Math.min(255, g));
                b = Math.max(0, Math.min(255, b));
                
                Color sharpenColor = new Color((int)r, (int)g, (int)b);
                result.setRGB(x, y, sharpenColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 浮雕滤镜
     */
    private BufferedImage applyEmbossFilter(BufferedImage original) {
        int width = original.getWidth();
        int height = original.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        // 浮雕卷积核
        float[] embossKernel = {
            -2, -1, 0,
            -1, 1, 1,
            0, 1, 2
        };
        
        for (int x = 1; x < width - 1; x++) {
            for (int y = 1; y < height - 1; y++) {
                float r = 0, g = 0, b = 0;
                
                for (int dx = -1; dx <= 1; dx++) {
                    for (int dy = -1; dy <= 1; dy++) {
                        Color color = new Color(original.getRGB(x + dx, y + dy));
                        float factor = embossKernel[(dy + 1) * 3 + (dx + 1)];
                        
                        r += color.getRed() * factor;
                        g += color.getGreen() * factor;
                        b += color.getBlue() * factor;
                    }
                }
                
                // 添加128作为中性灰
                r = Math.max(0, Math.min(255, r + 128));
                g = Math.max(0, Math.min(255, g + 128));
                b = Math.max(0, Math.min(255, b + 128));
                
                Color embossColor = new Color((int)r, (int)g, (int)b);
                result.setRGB(x, y, embossColor.getRGB());
            }
        }
        return result;
    }

    /**
     * 批量转换图片格式
     */
    public boolean convertFormat(String inputPath, String outputPath, String targetFormat) {
        try {
            BufferedImage image = ImageIO.read(new File(inputPath));
            if (image == null) {
                return false;
            }

            // 如果目标格式不支持透明度，转换为RGB
            if ("JPG".equalsIgnoreCase(targetFormat) || "JPEG".equalsIgnoreCase(targetFormat)) {
                BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = rgbImage.createGraphics();
                g2d.setColor(Color.WHITE);
                g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();
                image = rgbImage;
            }

            ImageIO.write(image, targetFormat, new File(outputPath));
            logger.info("格式转换成功: {} -> {}, 格式: {}", inputPath, outputPath, targetFormat);
            return true;

        } catch (IOException e) {
            logger.error("格式转换失败: {}", inputPath, e);
            return false;
        }
    }

    /**
     * 图片旋转
     */
    public boolean rotateImage(String inputPath, String outputPath, double angle) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(inputPath));
            if (originalImage == null) {
                return false;
            }

            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            // 计算旋转后的尺寸
            double radians = Math.toRadians(angle);
            double sin = Math.abs(Math.sin(radians));
            double cos = Math.abs(Math.cos(radians));
            int newWidth = (int) Math.floor(width * cos + height * sin);
            int newHeight = (int) Math.floor(height * cos + width * sin);

            BufferedImage rotatedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = rotatedImage.createGraphics();

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

            // 设置旋转中心
            g2d.translate((newWidth - width) / 2, (newHeight - height) / 2);
            g2d.rotate(radians, width / 2.0, height / 2.0);

            // 绘制原图
            g2d.drawImage(originalImage, 0, 0, null);
            g2d.dispose();

            ImageIO.write(rotatedImage, "JPG", new File(outputPath));
            logger.info("图片旋转成功: {} -> {}, 角度: {}°", inputPath, outputPath, angle);
            return true;

        } catch (IOException e) {
            logger.error("图片旋转失败: {}", inputPath, e);
            return false;
        }
    }
} 