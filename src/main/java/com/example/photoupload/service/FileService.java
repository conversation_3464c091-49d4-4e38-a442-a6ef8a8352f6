package com.example.photoupload.service;

import com.example.photoupload.dto.FileInfoResponse;
import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.dto.BatchProcessRequest;
import com.example.photoupload.dto.ImageAnalysisResult;
import com.example.photoupload.dto.ColorAnalysisResult;
import com.example.photoupload.entity.FileInfo;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件服务接口 - 增强版
 * 
 * <AUTHOR>
 * @version 2.0
 */
public interface FileService {

    /**
     * 上传单个文件
     */
    FileUploadResponse uploadFile(MultipartFile file, String uploaderIp) throws IOException;

    /**
     * 上传多个文件
     */
    List<FileUploadResponse> uploadFiles(MultipartFile[] files, String uploaderIp) throws IOException;

    /**
     * 下载文件
     */
    Resource downloadFile(String fileName) throws IOException;

    /**
     * 预览文件
     */
    Resource previewFile(String fileName) throws IOException;

    /**
     * 获取文件信息
     */
    FileInfoResponse getFileInfo(String fileName);

    /**
     * 获取所有文件列表
     */
    List<FileInfoResponse> getAllFiles();

    /**
     * 删除文件
     */
    boolean deleteFile(String fileName);

    /**
     * 批量删除文件
     */
    boolean deleteFiles(List<String> fileNames);

    /**
     * 检查文件是否存在
     */
    boolean fileExists(String fileName);

    /**
     * 获取文件缩略图
     */
    Resource getThumbnail(String fileName) throws IOException;

    /**
     * 清理过期文件
     */
    void cleanupExpiredFiles();

    /**
     * 获取存储统计信息
     */
    StorageStats getStorageStats();

    /**
     * 记录文件下载
     */
    void recordDownload(String fileName);

    /**
     * 添加文字水印
     */
    FileUploadResponse addTextWatermark(String fileName, String text, String position, 
                                      float opacity, int fontSize, String fontColor, String uploaderIp) throws IOException;

    /**
     * 添加图片水印
     */
    FileUploadResponse addImageWatermark(String fileName, String watermarkImagePath, String position, 
                                       float opacity, float scale, String uploaderIp) throws IOException;

    /**
     * 应用图片滤镜
     */
    FileUploadResponse applyFilter(String fileName, String filterType, String uploaderIp) throws IOException;

    /**
     * 图片格式转换
     */
    FileUploadResponse convertFormat(String fileName, String targetFormat, String uploaderIp) throws IOException;

    /**
     * 图片旋转
     */
    FileUploadResponse rotateImage(String fileName, double angle, String uploaderIp) throws IOException;

    /**
     * 生成二维码分享链接
     */
    Resource generateQRCode(String fileName) throws IOException;

    /**
     * 批量处理图片
     */
    List<FileUploadResponse> batchProcess(BatchProcessRequest request, String uploaderIp) throws IOException;

    /**
     * AI图片分析
     */
    ImageAnalysisResult analyzeImage(String fileName) throws IOException;

    /**
     * 色彩分析
     */
    ColorAnalysisResult analyzeColors(String fileName) throws IOException;

    /**
     * 图片去重检测
     */
    List<String> findDuplicateImages();

    /**
     * 图片相似度检测
     */
    List<SimilarImage> findSimilarImages(String fileName, double threshold);

    /**
     * 生成图片拼贴
     */
    FileUploadResponse createCollage(List<String> fileNames, String layout, String uploaderIp) throws IOException;

    /**
     * 存储统计信息内部类
     */
    class StorageStats {
        private long totalFiles;
        private long totalSize;
        private String formattedSize;

        public StorageStats(long totalFiles, long totalSize, String formattedSize) {
            this.totalFiles = totalFiles;
            this.totalSize = totalSize;
            this.formattedSize = formattedSize;
        }

        // Getters
        public long getTotalFiles() { return totalFiles; }
        public long getTotalSize() { return totalSize; }
        public String getFormattedSize() { return formattedSize; }
    }

    /**
     * 相似图片结果
     */
    class SimilarImage {
        private String fileName;
        private double similarity;
        private String reason;

        public SimilarImage(String fileName, double similarity, String reason) {
            this.fileName = fileName;
            this.similarity = similarity;
            this.reason = reason;
        }

        // Getters
        public String getFileName() { return fileName; }
        public double getSimilarity() { return similarity; }
        public String getReason() { return reason; }
    }
} 