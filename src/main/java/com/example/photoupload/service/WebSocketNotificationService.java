package com.example.photoupload.service;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.dto.WebSocketMessage;

/**
 * WebSocket通知服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface WebSocketNotificationService {
    
    /**
     * 发送上传进度通知
     * 
     * @param sessionId 会话ID
     * @param fileName 文件名
     * @param progress 进度百分比
     * @param bytesUploaded 已上传字节数
     * @param totalBytes 总字节数
     * @param speed 上传速度
     */
    void sendUploadProgress(String sessionId, String fileName, Integer progress, 
                           Long bytesUploaded, Long totalBytes, String speed);
    
    /**
     * 发送上传完成通知
     * 
     * @param sessionId 会话ID
     * @param fileName 文件名
     */
    void sendUploadComplete(String sessionId, String fileName);
    
    /**
     * 发送上传错误通知
     * 
     * @param sessionId 会话ID
     * @param fileName 文件名
     * @param errorMessage 错误信息
     */
    void sendUploadError(String sessionId, String fileName, String errorMessage);
    
    /**
     * 发送AI分析开始通知
     * 
     * @param fileName 文件名
     */
    void sendAIAnalysisStart(String fileName);
    
    /**
     * 发送AI分析完成通知
     * 
     * @param fileName 文件名
     * @param result 分析结果
     */
    void sendAIAnalysisComplete(String fileName, AIAnalysisResult result);
    
    /**
     * 发送AI分析错误通知
     * 
     * @param fileName 文件名
     * @param errorMessage 错误信息
     */
    void sendAIAnalysisError(String fileName, String errorMessage);
    
    /**
     * 发送系统通知
     * 
     * @param title 标题
     * @param message 消息内容
     * @param level 通知级别
     */
    void sendSystemNotification(String title, String message, WebSocketMessage.NotificationData.NotificationLevel level);
    
    /**
     * 发送用户通知
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param message 消息内容
     * @param level 通知级别
     */
    void sendUserNotification(String userId, String title, String message, 
                             WebSocketMessage.NotificationData.NotificationLevel level);
    
    /**
     * 发送批量处理开始通知
     * 
     * @param batchId 批次ID
     * @param totalFiles 总文件数
     * @param operation 操作类型
     */
    void sendBatchProcessStart(String batchId, Integer totalFiles, String operation);
    
    /**
     * 发送批量处理进度通知
     * 
     * @param batchId 批次ID
     * @param totalFiles 总文件数
     * @param processedFiles 已处理文件数
     * @param currentFile 当前处理文件
     * @param operation 操作类型
     */
    void sendBatchProcessProgress(String batchId, Integer totalFiles, Integer processedFiles, 
                                 String currentFile, String operation);
    
    /**
     * 发送批量处理完成通知
     * 
     * @param batchId 批次ID
     * @param totalFiles 总文件数
     * @param operation 操作类型
     */
    void sendBatchProcessComplete(String batchId, Integer totalFiles, String operation);
    
    /**
     * 发送文件分享通知
     * 
     * @param fileName 文件名
     * @param sharedBy 分享者
     * @param shareUrl 分享链接
     */
    void sendFileShared(String fileName, String sharedBy, String shareUrl);
    
    /**
     * 发送文件点赞通知
     * 
     * @param fileName 文件名
     * @param likedBy 点赞者
     */
    void sendFileLiked(String fileName, String likedBy);
    
    /**
     * 发送文件评论通知
     * 
     * @param fileName 文件名
     * @param commentedBy 评论者
     * @param comment 评论内容
     */
    void sendFileCommented(String fileName, String commentedBy, String comment);
    
    /**
     * 向指定会话发送消息
     * 
     * @param sessionId 会话ID
     * @param message 消息
     */
    void sendToSession(String sessionId, WebSocketMessage message);
    
    /**
     * 向指定用户发送消息
     * 
     * @param userId 用户ID
     * @param message 消息
     */
    void sendToUser(String userId, WebSocketMessage message);
    
    /**
     * 广播消息给所有连接的客户端
     * 
     * @param message 消息
     */
    void broadcast(WebSocketMessage message);
    
    /**
     * 获取在线用户数量
     * 
     * @return 在线用户数量
     */
    int getOnlineUserCount();
    
    /**
     * 获取活跃会话数量
     * 
     * @return 活跃会话数量
     */
    int getActiveSessionCount();
}
