package com.example.photoupload.service;

import java.io.File;
import java.io.IOException;

/**
 * 图片处理服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ImageProcessingService {

    /**
     * 压缩图片
     */
    File compressImage(File imageFile) throws IOException;

    /**
     * 生成缩略图
     */
    File generateThumbnail(File imageFile) throws IOException;

    /**
     * 调整图片大小
     */
    File resizeImage(File imageFile, int width, int height) throws IOException;

    /**
     * 裁剪图片
     */
    File cropImage(File imageFile, int x, int y, int width, int height) throws IOException;
} 