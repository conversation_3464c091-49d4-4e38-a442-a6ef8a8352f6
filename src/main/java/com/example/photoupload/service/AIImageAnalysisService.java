package com.example.photoupload.service;

import com.example.photoupload.dto.AIAnalysisResult;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.util.concurrent.CompletableFuture;

/**
 * AI图像分析服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface AIImageAnalysisService {
    
    /**
     * 分析图像内容
     * 
     * @param file 图像文件
     * @return AI分析结果
     */
    CompletableFuture<AIAnalysisResult> analyzeImage(File file);
    
    /**
     * 分析图像内容（MultipartFile版本）
     * 
     * @param file 图像文件
     * @return AI分析结果
     */
    CompletableFuture<AIAnalysisResult> analyzeImage(MultipartFile file);
    
    /**
     * 检测图像中的物体
     * 
     * @param file 图像文件
     * @return 物体检测结果
     */
    CompletableFuture<AIAnalysisResult> detectObjects(File file);
    
    /**
     * 检测图像中的人脸
     * 
     * @param file 图像文件
     * @return 人脸检测结果
     */
    CompletableFuture<AIAnalysisResult> detectFaces(File file);
    
    /**
     * 识别图像中的文字
     * 
     * @param file 图像文件
     * @return 文字识别结果
     */
    CompletableFuture<AIAnalysisResult> recognizeText(File file);
    
    /**
     * 分析图像颜色
     * 
     * @param file 图像文件
     * @return 颜色分析结果
     */
    CompletableFuture<AIAnalysisResult> analyzeColors(File file);
    
    /**
     * 生成图像标签
     * 
     * @param file 图像文件
     * @return 标签生成结果
     */
    CompletableFuture<AIAnalysisResult> generateLabels(File file);
    
    /**
     * 检查服务是否可用
     * 
     * @return 服务状态
     */
    boolean isServiceAvailable();
    
    /**
     * 获取支持的图像格式
     * 
     * @return 支持的格式列表
     */
    String[] getSupportedFormats();
}
