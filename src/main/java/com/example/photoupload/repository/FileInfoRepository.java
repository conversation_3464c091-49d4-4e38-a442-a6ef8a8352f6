package com.example.photoupload.repository;

import com.example.photoupload.entity.FileInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 文件信息数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface FileInfoRepository extends JpaRepository<FileInfo, Long> {

    /**
     * 根据存储文件名查找文件信息
     */
    Optional<FileInfo> findByStoredNameAndIsDeletedFalse(String storedName);

    /**
     * 根据MD5哈希值查找文件
     */
    Optional<FileInfo> findByMd5HashAndIsDeletedFalse(String md5Hash);

    /**
     * 查找所有未删除的文件
     */
    List<FileInfo> findByIsDeletedFalseOrderByUploadTimeDesc();

    /**
     * 根据内容类型查找文件
     */
    List<FileInfo> findByContentTypeContainingAndIsDeletedFalse(String contentType);

    /**
     * 根据上传时间范围查找文件
     */
    List<FileInfo> findByUploadTimeBetweenAndIsDeletedFalse(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计总文件数量
     */
    @Query("SELECT COUNT(f) FROM FileInfo f WHERE f.isDeleted = false")
    long countTotalFiles();

    /**
     * 统计总文件大小
     */
    @Query("SELECT COALESCE(SUM(f.fileSize), 0) FROM FileInfo f WHERE f.isDeleted = false")
    long sumTotalFileSize();

    /**
     * 查找指定时间之前的文件（用于清理）
     */
    List<FileInfo> findByUploadTimeBeforeAndIsDeletedFalse(LocalDateTime time);

    /**
     * 增加下载次数
     */
    @Modifying
    @Query("UPDATE FileInfo f SET f.downloadCount = f.downloadCount + 1, f.lastDownloadTime = :downloadTime WHERE f.id = :id")
    int incrementDownloadCount(@Param("id") Long id, @Param("downloadTime") LocalDateTime downloadTime);

    /**
     * 逻辑删除文件
     */
    @Modifying
    @Query("UPDATE FileInfo f SET f.isDeleted = true WHERE f.id = :id")
    int deleteFileLogically(@Param("id") Long id);

    /**
     * 批量逻辑删除文件
     */
    @Modifying
    @Query("UPDATE FileInfo f SET f.isDeleted = true WHERE f.id IN :ids")
    int deleteFilesLogically(@Param("ids") List<Long> ids);
} 