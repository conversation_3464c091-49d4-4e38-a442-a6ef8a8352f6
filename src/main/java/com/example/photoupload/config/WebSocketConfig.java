package com.example.photoupload.config;

import com.example.photoupload.service.impl.WebSocketNotificationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketConfig.class);
    
    // 移除直接注入，避免循环依赖

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 启用简单的消息代理，并设置消息代理的前缀
        config.enableSimpleBroker("/topic", "/queue");
        
        // 设置应用程序的目标前缀
        config.setApplicationDestinationPrefixes("/app");
        
        // 设置用户目标前缀
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册STOMP端点，并允许跨域
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
        
        // 注册原生WebSocket端点
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*");
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // 添加拦截器来处理连接和断开连接事件
        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
                
                if (accessor != null) {
                    String sessionId = accessor.getSessionId();
                    StompCommand command = accessor.getCommand();
                    
                    switch (command) {
                        case CONNECT:
                            handleConnect(accessor, sessionId);
                            break;
                        case DISCONNECT:
                            handleDisconnect(sessionId);
                            break;
                        case SUBSCRIBE:
                            handleSubscribe(accessor, sessionId);
                            break;
                        case UNSUBSCRIBE:
                            handleUnsubscribe(accessor, sessionId);
                            break;
                        default:
                            break;
                    }
                }
                
                return message;
            }
        });
    }

    /**
     * 处理客户端连接
     */
    private void handleConnect(StompHeaderAccessor accessor, String sessionId) {
        // 从请求头中获取用户信息
        String userId = accessor.getFirstNativeHeader("userId");
        if (userId == null || userId.trim().isEmpty()) {
            // 如果没有提供用户ID，使用会话ID作为用户ID
            userId = "guest_" + sessionId.substring(0, 8);
        }

        // 将用户信息存储到会话属性中
        accessor.getSessionAttributes().put("userId", userId);

        logger.info("WebSocket连接建立: sessionId={}, userId={}", sessionId, userId);
    }

    /**
     * 处理客户端断开连接
     */
    private void handleDisconnect(String sessionId) {
        logger.info("WebSocket连接断开: sessionId={}", sessionId);
    }

    /**
     * 处理客户端订阅
     */
    private void handleSubscribe(StompHeaderAccessor accessor, String sessionId) {
        String destination = accessor.getDestination();

        logger.debug("客户端订阅: sessionId={}, destination={}", sessionId, destination);

        // 可以在这里添加订阅权限检查
        if (destination != null) {
            if (destination.startsWith("/user/") && !destination.contains(sessionId)) {
                // 防止用户订阅其他用户的私有频道
                logger.warn("用户尝试订阅未授权的频道: sessionId={}, destination={}", sessionId, destination);
                // 这里可以抛出异常或者拒绝订阅
            }
        }
    }

    /**
     * 处理客户端取消订阅
     */
    private void handleUnsubscribe(StompHeaderAccessor accessor, String sessionId) {
        String destination = accessor.getDestination();

        logger.debug("客户端取消订阅: sessionId={}, destination={}", sessionId, destination);
    }
}
