package com.example.photoupload.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 文件上传路径
     */
    private String path = "./uploads/";

    /**
     * 允许的文件类型
     */
    private String allowedTypes = "jpg,jpeg,png,gif,bmp,webp";

    /**
     * 单文件最大大小(字节)
     */
    private long maxFileSize = 10485760L; // 10MB

    /**
     * 总上传大小限制(字节)
     */
    private long maxTotalSize = 104857600L; // 100MB

    /**
     * 图片压缩配置
     */
    private Compress compress = new Compress();

    /**
     * 缩略图配置
     */
    private Thumbnail thumbnail = new Thumbnail();

    // Get<PERSON> and Setters
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAllowedTypes() {
        return allowedTypes;
    }

    public void setAllowedTypes(String allowedTypes) {
        this.allowedTypes = allowedTypes;
    }

    public List<String> getAllowedTypesList() {
        return Arrays.asList(allowedTypes.toLowerCase().split(","));
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public long getMaxTotalSize() {
        return maxTotalSize;
    }

    public void setMaxTotalSize(long maxTotalSize) {
        this.maxTotalSize = maxTotalSize;
    }

    public Compress getCompress() {
        return compress;
    }

    public void setCompress(Compress compress) {
        this.compress = compress;
    }

    public Thumbnail getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(Thumbnail thumbnail) {
        this.thumbnail = thumbnail;
    }

    /**
     * 图片压缩配置
     */
    public static class Compress {
        private boolean enabled = true;
        private double quality = 0.8;
        private int maxWidth = 1920;
        private int maxHeight = 1080;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public double getQuality() {
            return quality;
        }

        public void setQuality(double quality) {
            this.quality = quality;
        }

        public int getMaxWidth() {
            return maxWidth;
        }

        public void setMaxWidth(int maxWidth) {
            this.maxWidth = maxWidth;
        }

        public int getMaxHeight() {
            return maxHeight;
        }

        public void setMaxHeight(int maxHeight) {
            this.maxHeight = maxHeight;
        }
    }

    /**
     * 缩略图配置
     */
    public static class Thumbnail {
        private boolean enabled = true;
        private int width = 200;
        private int height = 200;
        private String suffix = "_thumb";

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public String getSuffix() {
            return suffix;
        }

        public void setSuffix(String suffix) {
            this.suffix = suffix;
        }
    }
} 