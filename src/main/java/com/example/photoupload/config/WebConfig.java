package com.example.photoupload.config;

import com.example.photoupload.interceptor.FileAccessInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private FileAccessInterceptor fileAccessInterceptor;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射
        registry.addResourceHandler("/files/**")
                .addResourceLocations("file:" + fileUploadConfig.getPath())
                .setCachePeriod(3600); // 缓存1小时
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加文件访问拦截器
        registry.addInterceptor(fileAccessInterceptor)
                .addPathPatterns("/api/files/download/**", "/api/files/preview/**");
    }
} 