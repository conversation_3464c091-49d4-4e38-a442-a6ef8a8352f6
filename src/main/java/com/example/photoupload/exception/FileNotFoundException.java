package com.example.photoupload.exception;

/**
 * 文件未找到异常类
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileNotFoundException extends RuntimeException {

    private String errorCode;

    public FileNotFoundException(String message) {
        super(message);
    }

    public FileNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileNotFoundException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public FileNotFoundException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
} 