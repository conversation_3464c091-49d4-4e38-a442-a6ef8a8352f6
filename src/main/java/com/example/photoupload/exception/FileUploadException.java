package com.example.photoupload.exception;

/**
 * 文件上传异常类
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileUploadException extends RuntimeException {

    private String errorCode;

    public FileUploadException(String message) {
        super(message);
    }

    public FileUploadException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileUploadException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public FileUploadException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
} 