server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: photo-upload-system
  
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # H2控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # 缓存配置
  cache:
    type: simple

# 文件存储配置
file:
  upload:
    # 上传目录
    path: ./uploads/
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,webp
    # 单文件最大大小(字节)
    max-file-size: 10485760  # 10MB
    # 总上传大小限制(字节)
    max-total-size: 104857600  # 100MB
    # 图片压缩配置
    compress:
      enabled: true
      # 压缩质量 0.1-1.0
      quality: 0.8
      # 最大宽度
      max-width: 1920
      # 最大高度
      max-height: 1080
    # 缩略图配置
    thumbnail:
      enabled: true
      width: 200
      height: 200
      suffix: "_thumb"

# 安全配置
security:
  # 防盗链配置
  anti-hotlink:
    enabled: true
    allowed-domains: localhost,127.0.0.1
  # 访问控制
  access:
    # 是否需要认证
    auth-required: false
    # 允许的IP地址 (空表示不限制)
    allowed-ips: 

# 日志配置
logging:
  level:
    com.example.photoupload: DEBUG
    org.springframework.web.multipart: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/photo-upload-system.log

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized 