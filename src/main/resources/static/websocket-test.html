<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket实时通知测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .notifications {
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .notification {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .notification.success {
            border-left-color: #28a745;
        }
        .notification.warning {
            border-left-color: #ffc107;
        }
        .notification.error {
            border-left-color: #dc3545;
        }
        .notification .time {
            font-size: 12px;
            color: #666;
            float: right;
        }
        .notification .type {
            font-weight: bold;
            color: #333;
        }
        .notification .content {
            margin-top: 5px;
            color: #555;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WebSocket实时通知测试</h1>
            <p>测试照片上传系统的实时通知功能</p>
        </div>

        <div id="connectionStatus" class="status disconnected">
            ❌ 未连接到WebSocket服务器
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="messageCount">0</div>
                <div class="stat-label">收到消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="onlineUsers">0</div>
                <div class="stat-label">在线用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeSessions">0</div>
                <div class="stat-label">活跃会话</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="connect()">连接WebSocket</button>
            <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
            <button class="btn btn-success" onclick="sendHeartbeat()">发送心跳</button>
            <button class="btn btn-warning" onclick="simulateUpload()">模拟文件上传</button>
            <button class="btn btn-primary" onclick="simulateAIAnalysis()">模拟AI分析</button>
            <button class="btn btn-success" onclick="clearNotifications()">清空通知</button>
        </div>

        <h3>📢 实时通知</h3>
        <div id="notifications" class="notifications">
            <div class="notification">
                <span class="time">等待连接...</span>
                <div class="type">系统提示</div>
                <div class="content">点击"连接WebSocket"开始接收实时通知</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <script>
        let stompClient = null;
        let messageCount = 0;
        let connected = false;

        function connect() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({
                'userId': 'test_user_' + Math.random().toString(36).substr(2, 9)
            }, function (frame) {
                connected = true;
                updateConnectionStatus(true);
                addNotification('success', 'WebSocket连接', '成功连接到服务器');
                
                // 订阅公共通知
                stompClient.subscribe('/topic/notifications', function (message) {
                    handleMessage(JSON.parse(message.body));
                });
                
                // 订阅私人通知
                stompClient.subscribe('/user/queue/notifications', function (message) {
                    handleMessage(JSON.parse(message.body));
                });
                
                // 订阅心跳响应
                stompClient.subscribe('/topic/heartbeat', function (message) {
                    const data = JSON.parse(message.body);
                    updateStats(data.online_users, data.active_sessions);
                });
                
            }, function (error) {
                connected = false;
                updateConnectionStatus(false);
                addNotification('error', '连接错误', error);
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            connected = false;
            updateConnectionStatus(false);
            addNotification('warning', 'WebSocket断开', '已断开与服务器的连接');
        }

        function sendHeartbeat() {
            if (connected && stompClient) {
                stompClient.send("/app/heartbeat", {}, JSON.stringify({
                    'timestamp': new Date().getTime()
                }));
                addNotification('success', '心跳发送', '心跳消息已发送');
            } else {
                addNotification('error', '发送失败', '请先连接WebSocket');
            }
        }

        function simulateUpload() {
            if (connected) {
                // 模拟上传进度
                let progress = 0;
                const fileName = 'test_image_' + Date.now() + '.jpg';
                
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        addNotification('success', '上传完成', `文件 ${fileName} 上传完成`);
                    } else {
                        addNotification('success', '上传进度', `文件 ${fileName} 上传进度: ${Math.round(progress)}%`);
                    }
                }, 500);
            } else {
                addNotification('error', '模拟失败', '请先连接WebSocket');
            }
        }

        function simulateAIAnalysis() {
            if (connected) {
                const fileName = 'test_image_' + Date.now() + '.jpg';
                addNotification('success', 'AI分析开始', `开始分析文件: ${fileName}`);
                
                setTimeout(() => {
                    addNotification('success', 'AI分析完成', `文件 ${fileName} 分析完成，检测到3个物体，2个人脸`);
                }, 3000);
            } else {
                addNotification('error', '模拟失败', '请先连接WebSocket');
            }
        }

        function handleMessage(message) {
            messageCount++;
            document.getElementById('messageCount').textContent = messageCount;
            
            let type = 'success';
            let title = message.type || '未知消息';
            let content = JSON.stringify(message.data || message, null, 2);
            
            if (message.type) {
                switch (message.type) {
                    case 'UPLOAD_ERROR':
                    case 'AI_ANALYSIS_ERROR':
                        type = 'error';
                        break;
                    case 'UPLOAD_PROGRESS':
                    case 'BATCH_PROCESS_PROGRESS':
                        type = 'warning';
                        break;
                    default:
                        type = 'success';
                }
            }
            
            addNotification(type, title, content);
        }

        function updateConnectionStatus(isConnected) {
            const statusElement = document.getElementById('connectionStatus');
            if (isConnected) {
                statusElement.className = 'status connected';
                statusElement.innerHTML = '✅ 已连接到WebSocket服务器';
            } else {
                statusElement.className = 'status disconnected';
                statusElement.innerHTML = '❌ 未连接到WebSocket服务器';
            }
        }

        function updateStats(onlineUsers, activeSessions) {
            if (onlineUsers !== undefined) {
                document.getElementById('onlineUsers').textContent = onlineUsers;
            }
            if (activeSessions !== undefined) {
                document.getElementById('activeSessions').textContent = activeSessions;
            }
        }

        function addNotification(type, title, content) {
            const notificationsContainer = document.getElementById('notifications');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const time = new Date().toLocaleTimeString();
            notification.innerHTML = `
                <span class="time">${time}</span>
                <div class="type">${title}</div>
                <div class="content">${content}</div>
            `;
            
            notificationsContainer.insertBefore(notification, notificationsContainer.firstChild);
            
            // 限制通知数量
            const notifications = notificationsContainer.children;
            if (notifications.length > 50) {
                notificationsContainer.removeChild(notifications[notifications.length - 1]);
            }
        }

        function clearNotifications() {
            document.getElementById('notifications').innerHTML = '';
            messageCount = 0;
            document.getElementById('messageCount').textContent = '0';
            addNotification('success', '通知清空', '所有通知已清空');
        }

        // 页面加载时自动连接
        window.addEventListener('load', function() {
            setTimeout(connect, 1000);
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (connected) {
                disconnect();
            }
        });
    </script>
</body>
</html>
