<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 AI照片处理工作室</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --bg-light: #ffffff;
            --bg-dark: #1a1a2e;
            --card-light: #ffffff;
            --card-dark: #16213e;
            --text-light: #333333;
            --text-dark: #ffffff;
            --border-light: #e0e0e0;
            --border-dark: #3a3a5c;
            --success-color: #4caf50;
            --error-color: #f44336;
            --warning-color: #ff9800;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
            min-height: 100vh;
            background: var(--bg-light);
            color: var(--text-light);
        }

        body.dark-mode {
            background: var(--bg-dark);
            color: var(--text-dark);
        }

        body.dark-mode {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        body:not(.dark-mode) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            animation: slideInDown 1s ease-out;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .theme-toggle {
            position: absolute;
            top: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .card {
            background: var(--card-light);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--border-light);
            backdrop-filter: blur(20px);
        }

        .dark-mode .card {
            background: var(--card-dark);
            border-color: var(--border-dark);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .upload-area {
            border: 3px dashed var(--border-light);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .dark-mode .upload-area {
            border-color: var(--border-dark);
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .upload-area:hover::before {
            animation: shimmer 1.5s infinite;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: var(--primary-color);
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: bounce 2s infinite;
        }

        .upload-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .upload-hint {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .preview-area {
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 250px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .preview-image:hover {
            transform: scale(1.05);
        }

        .filters-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        .filter-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .filter-btn.active {
            background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
        }

        .btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(
                -45deg,
                rgba(255, 255, 255, .2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, .2) 50%,
                rgba(255, 255, 255, .2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 20px 20px;
            animation: move 1s linear infinite;
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: var(--card-light);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .gallery-item {
            background: var(--card-dark);
        }

        .gallery-item:hover {
            transform: translateY(-10px) rotateY(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .gallery-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.1);
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(240, 147, 251, 0.8));
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-actions {
            display: flex;
            gap: 10px;
        }

        .gallery-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .gallery-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .stat-card {
            background: var(--card-light);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid var(--border-light);
        }

        .dark-mode .stat-card {
            background: var(--card-dark);
            border-color: var(--border-dark);
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .message.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .message.success {
            background: linear-gradient(45deg, var(--success-color), #66bb6a);
            color: white;
        }

        .message.error {
            background: linear-gradient(45deg, var(--error-color), #e57373);
            color: white;
        }

        .message.warning {
            background: linear-gradient(45deg, var(--warning-color), #ffb74d);
            color: white;
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1) rotate(180deg);
        }

        input[type="file"] {
            display: none;
        }

        .filter-sepia { filter: sepia(100%); }
        .filter-grayscale { filter: grayscale(100%); }
        .filter-blur { filter: blur(2px); }
        .filter-brightness { filter: brightness(150%); }
        .filter-contrast { filter: contrast(150%); }
        .filter-vintage { filter: sepia(50%) contrast(120%) brightness(110%); }
        .filter-cool { filter: hue-rotate(90deg) saturate(150%); }
        .filter-warm { filter: hue-rotate(-30deg) saturate(120%); }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translate3d(0, -100%, 0);
            }
            to {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translate3d(0, 100%, 0);
            }
            to {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                transform: translate3d(0, 0, 0);
            }
            40%, 43% {
                animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                transform: translate3d(0, -30px, 0);
            }
            70% {
                animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        @keyframes move {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 20px 20px;
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <button class="theme-toggle" onclick="toggleTheme()">
                <i class="fas fa-moon" id="theme-icon"></i>
                <span id="theme-text">暗黑模式</span>
            </button>
            <h1>🎨 AI照片处理工作室</h1>
            <p>专业级照片上传、处理与分享平台</p>
        </header>

        <div class="main-content">
            <!-- 上传区域 -->
            <div class="card">
                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">点击或拖拽上传照片</div>
                    <div class="upload-hint">支持 JPG、PNG、GIF 格式，最大 10MB</div>
                </div>

                <input type="file" id="fileInput" multiple accept="image/*">

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div id="progressText">上传中... 0%</div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" onclick="uploadFiles()">
                        <i class="fas fa-upload"></i> 开始上传
                    </button>
                    <button class="btn" onclick="clearFiles()" style="background: linear-gradient(45deg, #f44336, #e91e63);">
                        <i class="fas fa-trash"></i> 清空选择
                    </button>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="card">
                <div class="preview-area" id="previewArea">
                    <i class="fas fa-image" style="font-size: 3rem; opacity: 0.3; margin-bottom: 20px;"></i>
                    <p style="opacity: 0.6;">选择图片后可在此预览和应用滤镜</p>
                </div>

                <div class="filters-panel" id="filtersPanel" style="display: none;">
                    <button class="filter-btn" onclick="applyFilter('')">原图</button>
                    <button class="filter-btn" onclick="applyFilter('sepia')">复古</button>
                    <button class="filter-btn" onclick="applyFilter('grayscale')">黑白</button>
                    <button class="filter-btn" onclick="applyFilter('blur')">模糊</button>
                    <button class="filter-btn" onclick="applyFilter('brightness')">明亮</button>
                    <button class="filter-btn" onclick="applyFilter('contrast')">对比</button>
                    <button class="filter-btn" onclick="applyFilter('vintage')">怀旧</button>
                    <button class="filter-btn" onclick="applyFilter('cool')">冷色</button>
                    <button class="filter-btn" onclick="applyFilter('warm')">暖色</button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-images"></i></div>
                <div class="stat-value" id="totalFiles">0</div>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                <div class="stat-value" id="totalSize">0 B</div>
                <div class="stat-label">存储空间</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-download"></i></div>
                <div class="stat-value" id="totalDownloads">0</div>
                <div class="stat-label">下载次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-star"></i></div>
                <div class="stat-value" id="averageRating">5.0</div>
                <div class="stat-label">用户评分</div>
            </div>
        </div>

        <!-- 图片画廊 -->
        <div class="gallery" id="gallery"></div>

        <!-- 消息提示 -->
        <div class="message" id="message"></div>

        <!-- 浮动按钮 -->
        <button class="floating-action" onclick="scrollToTop()" title="返回顶部">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <script>
        let selectedFiles = [];
        let currentFilter = '';
        let isDarkMode = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadGallery();
            setupDragAndDrop();
            
            // 检查本地存储的主题设置
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                toggleTheme();
            }
        });

        // 主题切换
        function toggleTheme() {
            isDarkMode = !isDarkMode;
            document.body.classList.toggle('dark-mode');
            
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');
            
            if (isDarkMode) {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '明亮模式';
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '暗黑模式';
                localStorage.setItem('theme', 'light');
            }
        }

        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.querySelector('.upload-area');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            uploadArea.addEventListener('drop', handleDrop, false);

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function highlight(e) {
                uploadArea.classList.add('dragover');
            }

            function unhighlight(e) {
                uploadArea.classList.remove('dragover');
            }

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
        }

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files);
            
            if (selectedFiles.length > 0) {
                showPreview(selectedFiles[0]);
                showMessage(`已选择 ${selectedFiles.length} 个文件`, 'success');
            }
        }

        // 显示预览
        function showPreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewArea = document.getElementById('previewArea');
                previewArea.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" id="previewImage" alt="预览图片">
                    <p style="margin-top: 15px; opacity: 0.8;">${file.name}</p>
                    <p style="font-size: 0.9rem; opacity: 0.6;">${formatFileSize(file.size)}</p>
                `;
                
                document.getElementById('filtersPanel').style.display = 'flex';
            };
            reader.readAsDataURL(file);
        }

        // 应用滤镜
        function applyFilter(filter) {
            currentFilter = filter;
            const previewImage = document.getElementById('previewImage');
            
            if (previewImage) {
                // 移除所有滤镜类
                previewImage.className = 'preview-image';
                
                // 应用新滤镜
                if (filter) {
                    previewImage.classList.add(`filter-${filter}`);
                }
                
                // 更新按钮状态
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                event.target.classList.add('active');
                
                // 添加脉冲动画
                previewImage.classList.add('pulse-animation');
                setTimeout(() => {
                    previewImage.classList.remove('pulse-animation');
                }, 500);
            }
        }

        // 上传文件
        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                showMessage('请先选择文件', 'warning');
                return;
            }

            const progressContainer = document.querySelector('.progress-container');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressContainer.style.display = 'block';

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await fetch('/api/files/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        const progress = ((i + 1) / selectedFiles.length) * 100;
                        progressFill.style.width = progress + '%';
                        progressText.textContent = `上传中... ${Math.round(progress)}%`;
                        
                        // 添加到画廊
                        addToGallery(result.data);
                        
                        if (i === selectedFiles.length - 1) {
                            showMessage('所有文件上传成功！', 'success');
                            setTimeout(() => {
                                progressContainer.style.display = 'none';
                                clearFiles();
                                loadStats();
                            }, 1000);
                        }
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    showMessage(`上传失败: ${error.message}`, 'error');
                    progressContainer.style.display = 'none';
                    break;
                }
            }
        }

        // 清空选择
        function clearFiles() {
            selectedFiles = [];
            document.getElementById('fileInput').value = '';
            document.getElementById('previewArea').innerHTML = `
                <i class="fas fa-image" style="font-size: 3rem; opacity: 0.3; margin-bottom: 20px;"></i>
                <p style="opacity: 0.6;">选择图片后可在此预览和应用滤镜</p>
            `;
            document.getElementById('filtersPanel').style.display = 'none';
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/files/stats');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalFiles').textContent = stats.totalFiles;
                    document.getElementById('totalSize').textContent = stats.formattedSize;
                    
                    // 模拟其他统计数据
                    document.getElementById('totalDownloads').textContent = stats.totalFiles * 3;
                    document.getElementById('averageRating').textContent = '4.9';
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // 加载画廊
        async function loadGallery() {
            try {
                const response = await fetch('/api/files/list');
                const result = await response.json();
                
                if (result.success) {
                    const gallery = document.getElementById('gallery');
                    gallery.innerHTML = '';
                    
                    result.data.forEach(file => {
                        addToGallery(file);
                    });
                }
            } catch (error) {
                console.error('Failed to load gallery:', error);
            }
        }

        // 添加到画廊
        function addToGallery(fileData) {
            const gallery = document.getElementById('gallery');
            const item = document.createElement('div');
            item.className = 'gallery-item';
            
            item.innerHTML = `
                <img src="/api/files/preview/${fileData.storedName}" 
                     alt="${fileData.originalName}" 
                     class="gallery-image"
                     onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"100\\" height=\\"100\\"><rect width=\\"100\\" height=\\"100\\" fill=\\"%23ddd\\"/><text x=\\"50\\" y=\\"50\\" text-anchor=\\"middle\\" dy=\\".3em\\">图片</text></svg>'">
                <div class="gallery-overlay">
                    <div class="gallery-actions">
                        <button class="gallery-btn" onclick="downloadFile('${fileData.storedName}')" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="gallery-btn" onclick="previewFile('${fileData.storedName}')" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="gallery-btn" onclick="shareFile('${fileData.storedName}')" title="分享">
                            <i class="fas fa-share"></i>
                        </button>
                        <button class="gallery-btn" onclick="deleteFile('${fileData.storedName}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <p style="color: white; margin-top: 10px; font-size: 0.9rem;">${fileData.originalName}</p>
                    <p style="color: rgba(255,255,255,0.8); font-size: 0.8rem;">${formatFileSize(fileData.fileSize)}</p>
                </div>
            `;
            
            gallery.appendChild(item);
            
            // 添加入场动画
            setTimeout(() => {
                item.style.animation = 'slideInUp 0.5s ease-out';
            }, 100);
        }

        // 文件操作函数
        async function downloadFile(fileName) {
            window.open(`/api/files/download/${fileName}`, '_blank');
            showMessage('开始下载...', 'success');
        }

        function previewFile(fileName) {
            window.open(`/api/files/preview/${fileName}`, '_blank');
        }

        function shareFile(fileName) {
            const url = `${window.location.origin}/api/files/preview/${fileName}`;
            if (navigator.share) {
                navigator.share({
                    title: '分享图片',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    showMessage('链接已复制到剪贴板', 'success');
                });
            }
        }

        async function deleteFile(fileName) {
            if (confirm('确定要删除这个文件吗？')) {
                try {
                    const response = await fetch(`/api/files/${fileName}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        showMessage('文件删除成功', 'success');
                        loadGallery();
                        loadStats();
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    showMessage(`删除失败: ${error.message}`, 'error');
                }
            }
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type} show`;
            
            setTimeout(() => {
                message.classList.remove('show');
            }, 5000);
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加一些交互效果
        document.addEventListener('mousemove', function(e) {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                } else {
                    card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
                }
            });
        });
    </script>
</body>
</html> 