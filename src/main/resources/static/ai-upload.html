<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能图片上传</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .upload-section {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-section.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }
        
        .results-section {
            margin-top: 30px;
            display: none;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .analysis-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .result-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .result-card h3::before {
            content: '🤖';
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 3px;
        }
        
        .confidence {
            opacity: 0.8;
            font-size: 11px;
        }
        
        .websocket-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            z-index: 1000;
        }
        
        .websocket-status.connected {
            background-color: #28a745;
        }
        
        .websocket-status.disconnected {
            background-color: #dc3545;
        }
        
        .notification {
            position: fixed;
            top: 70px;
            right: 20px;
            max-width: 300px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            border-left: 4px solid #667eea;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            border-left-color: #28a745;
        }
        
        .notification.error {
            border-left-color: #dc3545;
        }
        
        .notification.warning {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="websocket-status disconnected" id="wsStatus">
        ❌ WebSocket未连接
    </div>
    
    <div class="notification" id="notification">
        <div class="notification-content"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 AI智能图片上传</h1>
            <p>上传图片，体验AI智能分析功能</p>
        </div>
        
        <div class="main-content">
            <div class="upload-section" id="uploadSection">
                <div class="upload-icon">📸</div>
                <div class="upload-text">
                    拖拽图片到这里，或点击选择文件
                </div>
                <input type="file" id="fileInput" class="file-input" accept="image/*" multiple>
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                    选择图片
                </button>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备上传...</div>
            </div>
            
            <div class="results-section" id="resultsSection">
                <h2>📊 分析结果</h2>
                <img id="imagePreview" class="image-preview" alt="预览图片">
                <div class="analysis-results" id="analysisResults">
                    <!-- AI分析结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <script>
        let stompClient = null;
        let connected = false;
        let currentFileName = null;

        // WebSocket连接
        function connectWebSocket() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({
                'userId': 'ai_upload_user_' + Math.random().toString(36).substr(2, 9)
            }, function (frame) {
                connected = true;
                updateWebSocketStatus(true);
                
                // 订阅通知
                stompClient.subscribe('/topic/notifications', handleWebSocketMessage);
                stompClient.subscribe('/user/queue/notifications', handleWebSocketMessage);
                
            }, function (error) {
                connected = false;
                updateWebSocketStatus(false);
                console.error('WebSocket连接失败:', error);
            });
        }

        function handleWebSocketMessage(message) {
            const data = JSON.parse(message.body);
            
            switch (data.type) {
                case 'UPLOAD_PROGRESS':
                    updateProgress(data.data.progress, `上传进度: ${data.data.progress}%`);
                    break;
                case 'UPLOAD_COMPLETE':
                    showNotification('success', '上传完成', `文件 ${data.data.file_name} 上传成功`);
                    break;
                case 'AI_ANALYSIS_START':
                    showNotification('success', 'AI分析开始', `开始分析 ${data.data.file_name}`);
                    break;
                case 'AI_ANALYSIS_COMPLETE':
                    showNotification('success', 'AI分析完成', `${data.data.file_name} 分析完成`);
                    displayAnalysisResults(data.data.result);
                    break;
                case 'AI_ANALYSIS_ERROR':
                    showNotification('error', 'AI分析失败', data.data.error_message);
                    break;
            }
        }

        function updateWebSocketStatus(isConnected) {
            const statusElement = document.getElementById('wsStatus');
            if (isConnected) {
                statusElement.className = 'websocket-status connected';
                statusElement.innerHTML = '✅ WebSocket已连接';
            } else {
                statusElement.className = 'websocket-status disconnected';
                statusElement.innerHTML = '❌ WebSocket未连接';
            }
        }

        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            notification.className = `notification ${type}`;
            notification.querySelector('.notification-content').innerHTML = `
                <strong>${title}</strong><br>
                ${message}
            `;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 文件上传处理
        function setupFileUpload() {
            const uploadSection = document.getElementById('uploadSection');
            const fileInput = document.getElementById('fileInput');

            // 拖拽处理
            uploadSection.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadSection.classList.add('dragover');
            });

            uploadSection.addEventListener('dragleave', () => {
                uploadSection.classList.remove('dragover');
            });

            uploadSection.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadSection.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // 文件选择处理
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files[0]);
                }
            });

            // 点击上传区域
            uploadSection.addEventListener('click', () => {
                fileInput.click();
            });
        }

        function handleFileUpload(file) {
            if (!file.type.startsWith('image/')) {
                showNotification('error', '文件类型错误', '请选择图片文件');
                return;
            }

            currentFileName = file.name;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('imagePreview').src = e.target.result;
                document.getElementById('resultsSection').style.display = 'block';
            };
            reader.readAsDataURL(file);

            // 上传文件
            uploadFile(file);
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'block';

            fetch('/api/files/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProgress(100, '上传完成');
                    currentFileName = data.data.storedName;
                    
                    // 启动AI分析
                    setTimeout(() => {
                        startAIAnalysis(currentFileName);
                    }, 1000);
                } else {
                    showNotification('error', '上传失败', data.message);
                }
            })
            .catch(error => {
                console.error('上传错误:', error);
                showNotification('error', '上传失败', '网络错误');
            });
        }

        function startAIAnalysis(fileName) {
            fetch(`/api/files/analyze/async/${fileName}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('success', 'AI分析启动', '正在进行智能分析...');
                } else {
                    showNotification('error', '分析启动失败', data.message);
                }
            })
            .catch(error => {
                console.error('AI分析启动错误:', error);
                showNotification('error', '分析启动失败', '网络错误');
            });
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function displayAnalysisResults(result) {
            const resultsContainer = document.getElementById('analysisResults');
            resultsContainer.innerHTML = '';

            // 显示标签
            if (result.labels && result.labels.length > 0) {
                const labelsCard = createResultCard('🏷️ 图像标签', 
                    result.labels.map(label => 
                        `<span class="tag">${label.name} <span class="confidence">${Math.round(label.confidence * 100)}%</span></span>`
                    ).join('')
                );
                resultsContainer.appendChild(labelsCard);
            }

            // 显示物体检测
            if (result.objects && result.objects.length > 0) {
                const objectsCard = createResultCard('🎯 物体检测', 
                    result.objects.map(obj => 
                        `<span class="tag">${obj.name} <span class="confidence">${Math.round(obj.confidence * 100)}%</span></span>`
                    ).join('')
                );
                resultsContainer.appendChild(objectsCard);
            }

            // 显示人脸检测
            if (result.faces && result.faces.length > 0) {
                const facesCard = createResultCard('👤 人脸检测', 
                    `检测到 ${result.faces.length} 个人脸`
                );
                resultsContainer.appendChild(facesCard);
            }

            // 显示文字识别
            if (result.text && result.text.length > 0) {
                const textCard = createResultCard('📝 文字识别', 
                    result.text.map(text => `<div>${text.text}</div>`).join('')
                );
                resultsContainer.appendChild(textCard);
            }

            // 显示颜色分析
            if (result.colors) {
                const colorsCard = createResultCard('🎨 颜色分析', 
                    `主色调: ${result.colors.dominant_color || '未知'}`
                );
                resultsContainer.appendChild(colorsCard);
            }
        }

        function createResultCard(title, content) {
            const card = document.createElement('div');
            card.className = 'result-card';
            card.innerHTML = `
                <h3>${title}</h3>
                <div>${content}</div>
            `;
            return card;
        }

        // 初始化
        window.addEventListener('load', () => {
            setupFileUpload();
            connectWebSocket();
        });

        window.addEventListener('beforeunload', () => {
            if (connected && stompClient) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>
