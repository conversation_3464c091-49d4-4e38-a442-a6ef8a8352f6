# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.H2Dialect

# AI服务配置
ai:
  service:
    enabled: true
    mock: true

# 日志配置
logging:
  level:
    com.example.photoupload: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n"

# 文件上传配置
file:
  upload:
    path: ${java.io.tmpdir}/test-uploads
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,bmp,webp

# WebSocket配置
websocket:
  enabled: false  # 测试环境禁用WebSocket
