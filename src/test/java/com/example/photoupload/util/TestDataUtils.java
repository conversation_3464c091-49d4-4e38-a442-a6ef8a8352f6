package com.example.photoupload.util;

import com.example.photoupload.dto.AIAnalysisResult;
import org.springframework.mock.web.MockMultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试数据工具类
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TestDataUtils {

    /**
     * 创建测试图像文件
     */
    public static File createTestImageFile(Path tempDir, String fileName, int width, int height, Color backgroundColor) throws IOException {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(backgroundColor);
        g2d.fillRect(0, 0, width, height);
        g2d.dispose();

        File imageFile = tempDir.resolve(fileName).toFile();
        ImageIO.write(image, "jpg", imageFile);
        return imageFile;
    }

    /**
     * 创建带有图形的测试图像文件
     */
    public static File createTestImageWithShapes(Path tempDir, String fileName) throws IOException {
        BufferedImage image = new BufferedImage(200, 200, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 200, 200);
        
        // 绘制一些形状
        g2d.setColor(Color.RED);
        g2d.fillOval(50, 50, 100, 100);
        
        g2d.setColor(Color.BLUE);
        g2d.fillRect(25, 25, 50, 50);
        
        g2d.setColor(Color.GREEN);
        g2d.fillRect(125, 125, 50, 50);
        
        g2d.dispose();

        File imageFile = tempDir.resolve(fileName).toFile();
        ImageIO.write(image, "jpg", imageFile);
        return imageFile;
    }

    /**
     * 创建测试 MultipartFile
     */
    public static MockMultipartFile createTestMultipartFile(String fileName, String contentType, byte[] content) {
        return new MockMultipartFile("file", fileName, contentType, content);
    }

    /**
     * 创建图像内容的 MultipartFile
     */
    public static MockMultipartFile createImageMultipartFile(String fileName, int width, int height) throws IOException {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.CYAN);
        g2d.fillRect(0, 0, width, height);
        g2d.dispose();

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", baos);
        byte[] imageBytes = baos.toByteArray();

        return new MockMultipartFile("file", fileName, "image/jpeg", imageBytes);
    }

    /**
     * 创建模拟的 AI 分析结果
     */
    public static AIAnalysisResult createMockAnalysisResult() {
        AIAnalysisResult result = new AIAnalysisResult();
        
        // 设置标签
        List<AIAnalysisResult.Label> labels = new ArrayList<>();
        labels.add(new AIAnalysisResult.Label("测试标签1", 0.95, "测试类别"));
        labels.add(new AIAnalysisResult.Label("测试标签2", 0.87, "测试类别"));
        result.setLabels(labels);
        
        // 设置物体检测结果
        List<AIAnalysisResult.DetectedObject> objects = new ArrayList<>();
        AIAnalysisResult.DetectedObject.BoundingBox bbox = 
            new AIAnalysisResult.DetectedObject.BoundingBox(10, 10, 50, 50);
        objects.add(new AIAnalysisResult.DetectedObject("测试物体", 0.92, bbox));
        result.setObjects(objects);
        
        // 设置人脸检测结果
        List<AIAnalysisResult.Face> faces = new ArrayList<>();
        AIAnalysisResult.Face face = new AIAnalysisResult.Face();
        face.setConfidence(0.89);
        face.setGender("未知");
        face.setAgeRange(new AIAnalysisResult.Face.AgeRange(25, 35));
        
        Map<String, Double> emotions = new HashMap<>();
        emotions.put("快乐", 0.8);
        emotions.put("中性", 0.2);
        face.setEmotions(emotions);
        
        AIAnalysisResult.DetectedObject.BoundingBox faceBbox = 
            new AIAnalysisResult.DetectedObject.BoundingBox(20, 20, 60, 60);
        face.setBoundingBox(faceBbox);
        faces.add(face);
        result.setFaces(faces);
        
        // 设置文字检测结果
        List<AIAnalysisResult.TextDetection> textDetections = new ArrayList<>();
        AIAnalysisResult.DetectedObject.BoundingBox textBbox = 
            new AIAnalysisResult.DetectedObject.BoundingBox(30, 30, 100, 20);
        textDetections.add(new AIAnalysisResult.TextDetection("测试文字", 0.94, textBbox));
        result.setTextDetections(textDetections);
        
        // 设置颜色分析结果
        AIAnalysisResult.ColorAnalysis colorAnalysis = new AIAnalysisResult.ColorAnalysis();
        List<AIAnalysisResult.ColorAnalysis.DominantColor> dominantColors = new ArrayList<>();
        
        AIAnalysisResult.ColorAnalysis.DominantColor.RGB rgb = 
            new AIAnalysisResult.ColorAnalysis.DominantColor.RGB(255, 0, 0);
        dominantColors.add(new AIAnalysisResult.ColorAnalysis.DominantColor("#FF0000", 45.5, rgb));
        
        colorAnalysis.setDominantColors(dominantColors);
        List<String> palette = new ArrayList<>();
        palette.add("#FF0000");
        palette.add("#00FF00");
        palette.add("#0000FF");
        colorAnalysis.setColorPalette(palette);
        result.setColorAnalysis(colorAnalysis);
        
        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("test_mode", true);
        metadata.put("created_by", "TestDataUtils");
        result.setMetadata(metadata);
        
        // 设置其他属性
        result.setOverallConfidence(0.91);
        result.setProcessingTime(1500L);
        
        return result;
    }

    /**
     * 验证 AI 分析结果的基本结构
     */
    public static void validateAnalysisResult(AIAnalysisResult result) {
        if (result == null) {
            throw new AssertionError("分析结果不能为空");
        }
        
        if (result.getOverallConfidence() == null || 
            result.getOverallConfidence() < 0 || 
            result.getOverallConfidence() > 1) {
            throw new AssertionError("整体置信度必须在0-1之间");
        }
        
        if (result.getProcessingTime() == null || result.getProcessingTime() < 0) {
            throw new AssertionError("处理时间必须大于等于0");
        }
        
        // 验证标签
        if (result.getLabels() != null) {
            for (AIAnalysisResult.Label label : result.getLabels()) {
                if (label.getName() == null || label.getName().trim().isEmpty()) {
                    throw new AssertionError("标签名称不能为空");
                }
                if (label.getConfidence() == null || 
                    label.getConfidence() < 0 || 
                    label.getConfidence() > 1) {
                    throw new AssertionError("标签置信度必须在0-1之间");
                }
            }
        }
        
        // 验证物体检测结果
        if (result.getObjects() != null) {
            for (AIAnalysisResult.DetectedObject obj : result.getObjects()) {
                if (obj.getName() == null || obj.getName().trim().isEmpty()) {
                    throw new AssertionError("物体名称不能为空");
                }
                if (obj.getConfidence() == null || 
                    obj.getConfidence() < 0 || 
                    obj.getConfidence() > 1) {
                    throw new AssertionError("物体检测置信度必须在0-1之间");
                }
                if (obj.getBoundingBox() == null) {
                    throw new AssertionError("物体边界框不能为空");
                }
            }
        }
        
        // 验证颜色分析结果
        if (result.getColorAnalysis() != null && result.getColorAnalysis().getDominantColors() != null) {
            for (AIAnalysisResult.ColorAnalysis.DominantColor color : result.getColorAnalysis().getDominantColors()) {
                if (color.getColor() == null || !color.getColor().matches("#[0-9A-Fa-f]{6}")) {
                    throw new AssertionError("颜色值必须是有效的十六进制格式");
                }
                if (color.getPercentage() == null || color.getPercentage() < 0 || color.getPercentage() > 100) {
                    throw new AssertionError("颜色百分比必须在0-100之间");
                }
            }
        }
    }

    /**
     * 创建无效的 MultipartFile（用于测试异常情况）
     */
    public static MockMultipartFile createInvalidMultipartFile() {
        return new MockMultipartFile("file", "invalid.txt", "text/plain", "这不是图像内容".getBytes());
    }
}
