package com.example.photoupload.service.impl;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.service.WebSocketNotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AI图像分析服务实现类测试
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class AIImageAnalysisServiceImplTest {

    @Mock
    private WebSocketNotificationService notificationService;

    @InjectMocks
    private AIImageAnalysisServiceImpl aiImageAnalysisService;

    @TempDir
    Path tempDir;

    private File testImageFile;
    private MockMultipartFile testMultipartFile;

    @BeforeEach
    void setUp() throws IOException {
        // 设置配置属性
        ReflectionTestUtils.setField(aiImageAnalysisService, "aiServiceEnabled", true);
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", true);

        // 创建测试图像文件
        testImageFile = createTestImageFile();
        
        // 创建测试 MultipartFile
        testMultipartFile = new MockMultipartFile(
                "file",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
    }

    private File createTestImageFile() throws IOException {
        // 创建一个简单的测试图像
        BufferedImage image = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.BLUE);
        g2d.fillRect(0, 0, 100, 100);
        g2d.setColor(Color.RED);
        g2d.fillOval(25, 25, 50, 50);
        g2d.dispose();

        File imageFile = tempDir.resolve("test-image.jpg").toFile();
        ImageIO.write(image, "jpg", imageFile);
        return imageFile;
    }

    @Test
    void testAnalyzeImage_File_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getLabels());
        assertNotNull(result.getObjects());
        assertNotNull(result.getFaces());
        assertNotNull(result.getTextDetections());
        assertNotNull(result.getColorAnalysis());
        assertNotNull(result.getMetadata());
        assertNotNull(result.getOverallConfidence());
        assertNotNull(result.getProcessingTime());

        assertTrue(result.getOverallConfidence() >= 0.8);
        assertTrue(result.getOverallConfidence() <= 1.0);
        assertTrue(result.getProcessingTime() > 0);

        // 验证通知服务调用
        verify(notificationService, times(1)).sendAIAnalysisStart(testImageFile.getName());
        verify(notificationService, times(1)).sendAIAnalysisComplete(eq(testImageFile.getName()), any(AIAnalysisResult.class));
        verify(notificationService, never()).sendAIAnalysisError(anyString(), anyString());
    }

    @Test
    void testAnalyzeImage_File_WithException() {
        // Given
        doThrow(new RuntimeException("WebSocket error")).when(notificationService)
                .sendAIAnalysisStart(anyString());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            try {
                aiImageAnalysisService.analyzeImage(testImageFile).get();
            } catch (ExecutionException e) {
                throw e.getCause();
            }
        });

        verify(notificationService, times(1)).sendAIAnalysisError(eq(testImageFile.getName()), anyString());
    }

    @Test
    void testAnalyzeImage_MultipartFile_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testMultipartFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getLabels());
        assertNotNull(result.getObjects());
        assertNotNull(result.getFaces());
        assertNotNull(result.getTextDetections());
        assertNotNull(result.getColorAnalysis());
        assertNotNull(result.getMetadata());
        assertNotNull(result.getOverallConfidence());
        assertNotNull(result.getProcessingTime());

        assertTrue(result.getOverallConfidence() >= 0.8);
        assertTrue(result.getOverallConfidence() <= 1.0);
    }

    @Test
    void testAnalyzeImage_MultipartFile_WithIOException() {
        // Given
        MockMultipartFile invalidFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                "invalid content".getBytes()
        ) {
            @Override
            public void transferTo(File dest) throws IOException {
                throw new IOException("Transfer failed");
            }
        };

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            try {
                aiImageAnalysisService.analyzeImage(invalidFile).get();
            } catch (ExecutionException e) {
                throw e.getCause();
            }
        });
    }

    @Test
    void testDetectObjects_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectObjects(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getObjects());
        assertFalse(result.getObjects().isEmpty());

        for (AIAnalysisResult.DetectedObject obj : result.getObjects()) {
            assertNotNull(obj.getName());
            assertNotNull(obj.getConfidence());
            assertNotNull(obj.getBoundingBox());
            assertTrue(obj.getConfidence() >= 0.7);
            assertTrue(obj.getConfidence() <= 1.0);
        }
    }

    @Test
    void testDetectFaces_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectFaces(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getFaces());
        
        // 验证人脸检测结果（如果有的话）
        for (AIAnalysisResult.Face face : result.getFaces()) {
            assertNotNull(face.getConfidence());
            assertNotNull(face.getBoundingBox());
            assertNotNull(face.getAgeRange());
            assertNotNull(face.getGender());
            assertNotNull(face.getEmotions());
            assertTrue(face.getConfidence() >= 0.8);
            assertTrue(face.getConfidence() <= 1.0);
        }
    }

    @Test
    void testRecognizeText_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.recognizeText(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getTextDetections());
        
        // 验证文字检测结果（如果有的话）
        for (AIAnalysisResult.TextDetection text : result.getTextDetections()) {
            assertNotNull(text.getText());
            assertNotNull(text.getConfidence());
            assertNotNull(text.getBoundingBox());
            assertTrue(text.getConfidence() >= 0.8);
            assertTrue(text.getConfidence() <= 1.0);
        }
    }

    @Test
    void testAnalyzeColors_Success_MockMode() throws ExecutionException, InterruptedException {
        // Given - 确保是 mock 模式
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", true);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeColors(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getColorAnalysis());
        assertNotNull(result.getColorAnalysis().getDominantColors());
        assertNotNull(result.getColorAnalysis().getColorPalette());
        assertFalse(result.getColorAnalysis().getDominantColors().isEmpty());
        
        for (AIAnalysisResult.ColorAnalysis.DominantColor color : result.getColorAnalysis().getDominantColors()) {
            assertNotNull(color.getColor());
            assertNotNull(color.getPercentage());
            assertNotNull(color.getRgb());
            assertTrue(color.getPercentage() >= 10.0);
            assertTrue(color.getPercentage() <= 40.0);
        }
    }

    @Test
    void testAnalyzeColors_Success_RealMode() throws ExecutionException, InterruptedException {
        // Given - 设置为真实模式
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeColors(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getColorAnalysis());
        assertNotNull(result.getColorAnalysis().getDominantColors());
        assertFalse(result.getColorAnalysis().getDominantColors().isEmpty());
        
        for (AIAnalysisResult.ColorAnalysis.DominantColor color : result.getColorAnalysis().getDominantColors()) {
            assertNotNull(color.getColor());
            assertNotNull(color.getPercentage());
            assertNotNull(color.getRgb());
            assertTrue(color.getPercentage() > 0);
        }
    }

    @Test
    void testGenerateLabels_Success() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.generateLabels(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getLabels());
        assertFalse(result.getLabels().isEmpty());
        assertTrue(result.getLabels().size() >= 3);
        assertTrue(result.getLabels().size() <= 7);
        
        for (AIAnalysisResult.Label label : result.getLabels()) {
            assertNotNull(label.getName());
            assertNotNull(label.getConfidence());
            assertNotNull(label.getCategory());
            assertTrue(label.getConfidence() >= 0.6);
            assertTrue(label.getConfidence() <= 1.0);
        }
    }

    @Test
    void testIsServiceAvailable_True() {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "aiServiceEnabled", true);

        // When
        boolean available = aiImageAnalysisService.isServiceAvailable();

        // Then
        assertTrue(available);
    }

    @Test
    void testIsServiceAvailable_False() {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "aiServiceEnabled", false);

        // When
        boolean available = aiImageAnalysisService.isServiceAvailable();

        // Then
        assertFalse(available);
    }

    @Test
    void testGetSupportedFormats() {
        // When
        String[] formats = aiImageAnalysisService.getSupportedFormats();

        // Then
        assertNotNull(formats);
        assertEquals(6, formats.length);
        assertArrayEquals(new String[]{"jpg", "jpeg", "png", "gif", "bmp", "webp"}, formats);
    }

    @Test
    void testAnalyzeColors_WithIOException() throws IOException {
        // Given
        File invalidFile = tempDir.resolve("invalid.txt").toFile();
        invalidFile.createNewFile(); // 创建一个非图像文件
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeColors(invalidFile);
        AIAnalysisResult result = future.join();

        // Then
        assertNotNull(result);
        // 在异常情况下，colorAnalysis 应该为 null 或空
        if (result.getColorAnalysis() != null) {
            assertTrue(result.getColorAnalysis().getDominantColors() == null ||
                      result.getColorAnalysis().getDominantColors().isEmpty());
        }
    }

    @Test
    void testAnalyzeImage_NonMockMode() throws ExecutionException, InterruptedException {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        // 在非 mock 模式下，callRealAIService 实际上还是调用 performMockAnalysis
        assertNotNull(result.getLabels());
        assertNotNull(result.getObjects());
        assertNotNull(result.getFaces());
        assertNotNull(result.getTextDetections());
        assertNotNull(result.getColorAnalysis());
        assertNotNull(result.getMetadata());
        assertNotNull(result.getOverallConfidence());
        assertNotNull(result.getProcessingTime());
    }

    @Test
    void testDetectObjects_NonMockMode() throws ExecutionException, InterruptedException {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectObjects(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        // 在非 mock 模式下，objects 应该为空或 null
        assertTrue(result.getObjects() == null || result.getObjects().isEmpty());
    }

    @Test
    void testDetectFaces_NonMockMode() throws ExecutionException, InterruptedException {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.detectFaces(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        // 在非 mock 模式下，faces 应该为空或 null
        assertTrue(result.getFaces() == null || result.getFaces().isEmpty());
    }

    @Test
    void testRecognizeText_NonMockMode() throws ExecutionException, InterruptedException {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.recognizeText(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        // 在非 mock 模式下，textDetections 应该为空或 null
        assertTrue(result.getTextDetections() == null || result.getTextDetections().isEmpty());
    }

    @Test
    void testGenerateLabels_NonMockMode() throws ExecutionException, InterruptedException {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "mockMode", false);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.generateLabels(testImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        // 在非 mock 模式下，labels 应该为空或 null
        assertTrue(result.getLabels() == null || result.getLabels().isEmpty());
    }

    @Test
    void testAnalyzeImage_File_NullFile() {
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            try {
                aiImageAnalysisService.analyzeImage((File) null).get();
            } catch (ExecutionException e) {
                throw e.getCause();
            }
        });
    }

    @Test
    void testAnalyzeImage_MultipartFile_NullFile() {
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            try {
                aiImageAnalysisService.analyzeImage((MockMultipartFile) null).get();
            } catch (ExecutionException e) {
                throw e.getCause();
            }
        });
    }

    @Test
    void testMockAnalysisResultStructure() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then - 验证模拟分析结果的结构
        assertNotNull(result.getMetadata());
        assertTrue(result.getMetadata().containsKey("file_name"));
        assertTrue(result.getMetadata().containsKey("file_size"));
        assertTrue(result.getMetadata().containsKey("analysis_time"));
        assertTrue(result.getMetadata().containsKey("model_version"));
        assertEquals("mock-v1.0", result.getMetadata().get("model_version"));
        assertEquals(testImageFile.getName(), result.getMetadata().get("file_name"));
        assertEquals(testImageFile.length(), result.getMetadata().get("file_size"));

        // 验证标签结构
        if (!result.getLabels().isEmpty()) {
            AIAnalysisResult.Label firstLabel = result.getLabels().get(0);
            assertNotNull(firstLabel.getName());
            assertNotNull(firstLabel.getConfidence());
            assertNotNull(firstLabel.getCategory());
            assertTrue(Arrays.asList("人物", "交通工具", "动物", "植物", "建筑", "其他")
                    .contains(firstLabel.getCategory()));
        }

        // 验证物体检测结构
        if (!result.getObjects().isEmpty()) {
            AIAnalysisResult.DetectedObject firstObject = result.getObjects().get(0);
            assertNotNull(firstObject.getBoundingBox());
            assertTrue(firstObject.getBoundingBox().getX() >= 0);
            assertTrue(firstObject.getBoundingBox().getY() >= 0);
            assertTrue(firstObject.getBoundingBox().getWidth() > 0);
            assertTrue(firstObject.getBoundingBox().getHeight() > 0);
        }

        // 验证颜色分析结构
        if (result.getColorAnalysis() != null && !result.getColorAnalysis().getDominantColors().isEmpty()) {
            AIAnalysisResult.ColorAnalysis.DominantColor firstColor =
                result.getColorAnalysis().getDominantColors().get(0);
            assertNotNull(firstColor.getColor());
            assertTrue(firstColor.getColor().startsWith("#"));
            assertEquals(7, firstColor.getColor().length()); // #RRGGBB
            assertNotNull(firstColor.getRgb());
            assertTrue(firstColor.getRgb().getR() >= 0 && firstColor.getRgb().getR() <= 255);
            assertTrue(firstColor.getRgb().getG() >= 0 && firstColor.getRgb().getG() <= 255);
            assertTrue(firstColor.getRgb().getB() >= 0 && firstColor.getRgb().getB() <= 255);
        }
    }

    @Test
    void testConcurrentAnalysis() throws InterruptedException {
        // Given
        int numberOfThreads = 5;
        CompletableFuture<AIAnalysisResult>[] futures = new CompletableFuture[numberOfThreads];

        // When - 并发执行多个分析任务
        for (int i = 0; i < numberOfThreads; i++) {
            futures[i] = aiImageAnalysisService.analyzeImage(testImageFile);
        }

        // Then - 等待所有任务完成并验证结果
        for (CompletableFuture<AIAnalysisResult> future : futures) {
            AIAnalysisResult result = future.join();
            assertNotNull(result);
            assertNotNull(result.getOverallConfidence());
            assertTrue(result.getOverallConfidence() >= 0.8);
            assertTrue(result.getOverallConfidence() <= 1.0);
        }

        // 验证通知服务被调用了正确的次数
        verify(notificationService, times(numberOfThreads)).sendAIAnalysisStart(anyString());
        verify(notificationService, times(numberOfThreads)).sendAIAnalysisComplete(anyString(), any(AIAnalysisResult.class));
    }

    @Test
    void testAnalysisPerformance() throws ExecutionException, InterruptedException {
        // When
        long startTime = System.currentTimeMillis();
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();
        long endTime = System.currentTimeMillis();

        // Then
        assertNotNull(result);
        assertNotNull(result.getProcessingTime());

        // 验证处理时间在合理范围内（模拟模式下应该在1-3秒之间）
        long actualTime = endTime - startTime;
        assertTrue(actualTime >= 1000, "分析时间应该至少1秒（模拟延迟）");
        assertTrue(actualTime <= 5000, "分析时间不应该超过5秒");

        // 验证记录的处理时间与实际时间相近
        assertTrue(Math.abs(result.getProcessingTime() - actualTime) <= 100,
                "记录的处理时间应该与实际时间相近");
    }

    @Test
    void testLargeImageFile() throws IOException, ExecutionException, InterruptedException {
        // Given - 创建一个较大的测试图像
        BufferedImage largeImage = new BufferedImage(1000, 1000, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = largeImage.createGraphics();
        g2d.setColor(Color.GREEN);
        g2d.fillRect(0, 0, 1000, 1000);
        g2d.setColor(Color.YELLOW);
        g2d.fillOval(200, 200, 600, 600);
        g2d.dispose();

        File largeImageFile = tempDir.resolve("large-test-image.jpg").toFile();
        ImageIO.write(largeImage, "jpg", largeImageFile);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(largeImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getOverallConfidence());
        assertTrue(result.getOverallConfidence() >= 0.8);
        assertTrue(result.getOverallConfidence() <= 1.0);

        // 验证元数据包含正确的文件大小
        assertTrue((Long) result.getMetadata().get("file_size") > testImageFile.length());
    }

    @Test
    void testSmallImageFile() throws IOException, ExecutionException, InterruptedException {
        // Given - 创建一个很小的测试图像
        BufferedImage smallImage = new BufferedImage(10, 10, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = smallImage.createGraphics();
        g2d.setColor(Color.BLACK);
        g2d.fillRect(0, 0, 10, 10);
        g2d.dispose();

        File smallImageFile = tempDir.resolve("small-test-image.jpg").toFile();
        ImageIO.write(smallImage, "jpg", smallImageFile);

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(smallImageFile);
        AIAnalysisResult result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getOverallConfidence());
        assertTrue(result.getOverallConfidence() >= 0.8);
        assertTrue(result.getOverallConfidence() <= 1.0);
    }

    @Test
    void testDifferentImageFormats() throws IOException, ExecutionException, InterruptedException {
        // Given - 创建不同格式的图像文件
        BufferedImage image = new BufferedImage(50, 50, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.MAGENTA);
        g2d.fillRect(0, 0, 50, 50);
        g2d.dispose();

        File pngFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(image, "png", pngFile);

        File bmpFile = tempDir.resolve("test.bmp").toFile();
        ImageIO.write(image, "bmp", bmpFile);

        // When & Then - 测试 PNG 格式
        CompletableFuture<AIAnalysisResult> pngFuture = aiImageAnalysisService.analyzeImage(pngFile);
        AIAnalysisResult pngResult = pngFuture.get();
        assertNotNull(pngResult);
        assertNotNull(pngResult.getOverallConfidence());

        // When & Then - 测试 BMP 格式
        CompletableFuture<AIAnalysisResult> bmpFuture = aiImageAnalysisService.analyzeImage(bmpFile);
        AIAnalysisResult bmpResult = bmpFuture.get();
        assertNotNull(bmpResult);
        assertNotNull(bmpResult.getOverallConfidence());
    }

    @Test
    void testEmptyMultipartFile() throws ExecutionException, InterruptedException {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "empty.jpg",
                "image/jpeg",
                new byte[0]
        );

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(emptyFile);
        AIAnalysisResult result = future.get();

        // Then - 空文件也能成功处理（创建空的临时文件）
        assertNotNull(result);
        assertNotNull(result.getOverallConfidence());
    }

    @Test
    void testMultipartFileWithNullOriginalFilename() throws ExecutionException, InterruptedException {
        // Given
        MockMultipartFile fileWithNullName = new MockMultipartFile(
                "file",
                null, // null original filename
                "image/jpeg",
                "test content".getBytes()
        );

        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(fileWithNullName);
        AIAnalysisResult result = future.get();

        // Then - null 文件名也能成功处理（会使用 "_null" 作为后缀）
        assertNotNull(result);
        assertNotNull(result.getOverallConfidence());
    }

    @Test
    void testServiceDisabled() {
        // Given
        ReflectionTestUtils.setField(aiImageAnalysisService, "aiServiceEnabled", false);

        // When
        boolean available = aiImageAnalysisService.isServiceAvailable();

        // Then
        assertFalse(available);
    }

    @Test
    void testRandomnessInMockResults() throws ExecutionException, InterruptedException {
        // Given - 执行多次分析以验证随机性
        int iterations = 5;

        // When & Then
        for (int i = 0; i < iterations; i++) {
            CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
            AIAnalysisResult result = future.get();

            assertNotNull(result);

            // 验证标签数量的随机性（应该在3-7之间）
            assertTrue(result.getLabels().size() >= 3);
            assertTrue(result.getLabels().size() <= 7);

            // 验证物体数量的随机性（应该在1-4之间）
            assertTrue(result.getObjects().size() >= 1);
            assertTrue(result.getObjects().size() <= 4);

            // 验证置信度的随机性
            assertTrue(result.getOverallConfidence() >= 0.8);
            assertTrue(result.getOverallConfidence() <= 1.0);
        }
    }
}
