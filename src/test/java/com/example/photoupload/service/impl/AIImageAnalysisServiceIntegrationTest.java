package com.example.photoupload.service.impl;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.service.AIImageAnalysisService;
import com.example.photoupload.util.TestDataUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI图像分析服务集成测试
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootTest
@ActiveProfiles("test")
class AIImageAnalysisServiceIntegrationTest {

    @Autowired
    private AIImageAnalysisService aiImageAnalysisService;

    @TempDir
    Path tempDir;

    private File testImageFile;
    private MockMultipartFile testMultipartFile;

    @BeforeEach
    void setUp() throws IOException {
        // 创建测试图像文件
        testImageFile = TestDataUtils.createTestImageWithShapes(tempDir, "integration-test.jpg");
        
        // 创建测试 MultipartFile
        testMultipartFile = TestDataUtils.createImageMultipartFile("integration-test.jpg", 150, 150);
    }

    @Test
    void testCompleteAnalysisWorkflow() throws ExecutionException, InterruptedException {
        // When - 执行完整的图像分析
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then - 验证分析结果
        assertNotNull(result, "分析结果不能为空");
        TestDataUtils.validateAnalysisResult(result);
        
        // 验证各个分析组件都有结果
        assertNotNull(result.getLabels(), "标签分析结果不能为空");
        assertNotNull(result.getObjects(), "物体检测结果不能为空");
        assertNotNull(result.getFaces(), "人脸检测结果不能为空");
        assertNotNull(result.getTextDetections(), "文字识别结果不能为空");
        assertNotNull(result.getColorAnalysis(), "颜色分析结果不能为空");
        assertNotNull(result.getMetadata(), "元数据不能为空");
        
        // 验证处理时间合理
        assertTrue(result.getProcessingTime() > 0, "处理时间应该大于0");
        assertTrue(result.getProcessingTime() < 10000, "处理时间不应该超过10秒");
        
        // 验证置信度合理
        assertTrue(result.getOverallConfidence() >= 0.8, "整体置信度应该较高");
        assertTrue(result.getOverallConfidence() <= 1.0, "整体置信度不能超过1.0");
    }

    @Test
    void testMultipartFileAnalysisWorkflow() throws ExecutionException, InterruptedException {
        // When - 使用 MultipartFile 进行分析
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testMultipartFile);
        AIAnalysisResult result = future.get();

        // Then - 验证分析结果
        assertNotNull(result, "分析结果不能为空");
        TestDataUtils.validateAnalysisResult(result);
        
        // 验证临时文件被正确清理（通过检查结果是否正常生成来间接验证）
        assertNotNull(result.getMetadata(), "元数据应该包含文件信息");
    }

    @Test
    void testIndividualAnalysisMethods() throws ExecutionException, InterruptedException {
        // Test object detection
        CompletableFuture<AIAnalysisResult> objectsFuture = aiImageAnalysisService.detectObjects(testImageFile);
        AIAnalysisResult objectsResult = objectsFuture.get();
        assertNotNull(objectsResult);
        assertNotNull(objectsResult.getObjects());

        // Test face detection
        CompletableFuture<AIAnalysisResult> facesFuture = aiImageAnalysisService.detectFaces(testImageFile);
        AIAnalysisResult facesResult = facesFuture.get();
        assertNotNull(facesResult);
        assertNotNull(facesResult.getFaces());

        // Test text recognition
        CompletableFuture<AIAnalysisResult> textFuture = aiImageAnalysisService.recognizeText(testImageFile);
        AIAnalysisResult textResult = textFuture.get();
        assertNotNull(textResult);
        assertNotNull(textResult.getTextDetections());

        // Test color analysis
        CompletableFuture<AIAnalysisResult> colorsFuture = aiImageAnalysisService.analyzeColors(testImageFile);
        AIAnalysisResult colorsResult = colorsFuture.get();
        assertNotNull(colorsResult);
        assertNotNull(colorsResult.getColorAnalysis());

        // Test label generation
        CompletableFuture<AIAnalysisResult> labelsFuture = aiImageAnalysisService.generateLabels(testImageFile);
        AIAnalysisResult labelsResult = labelsFuture.get();
        assertNotNull(labelsResult);
        assertNotNull(labelsResult.getLabels());
    }

    @Test
    void testServiceAvailability() {
        // When
        boolean available = aiImageAnalysisService.isServiceAvailable();
        String[] formats = aiImageAnalysisService.getSupportedFormats();

        // Then
        assertTrue(available, "AI服务应该可用");
        assertNotNull(formats, "支持的格式列表不能为空");
        assertTrue(formats.length > 0, "应该支持至少一种格式");
        
        // 验证常见格式都被支持
        boolean supportsJpg = false;
        boolean supportsPng = false;
        for (String format : formats) {
            if ("jpg".equals(format) || "jpeg".equals(format)) {
                supportsJpg = true;
            }
            if ("png".equals(format)) {
                supportsPng = true;
            }
        }
        assertTrue(supportsJpg, "应该支持JPG格式");
        assertTrue(supportsPng, "应该支持PNG格式");
    }

    @Test
    void testDifferentImageSizes() throws IOException, ExecutionException, InterruptedException {
        // Test small image
        File smallImage = TestDataUtils.createTestImageFile(tempDir, "small.jpg", 50, 50, Color.RED);
        CompletableFuture<AIAnalysisResult> smallFuture = aiImageAnalysisService.analyzeImage(smallImage);
        AIAnalysisResult smallResult = smallFuture.get();
        assertNotNull(smallResult);
        TestDataUtils.validateAnalysisResult(smallResult);

        // Test large image
        File largeImage = TestDataUtils.createTestImageFile(tempDir, "large.jpg", 1000, 1000, Color.BLUE);
        CompletableFuture<AIAnalysisResult> largeFuture = aiImageAnalysisService.analyzeImage(largeImage);
        AIAnalysisResult largeResult = largeFuture.get();
        assertNotNull(largeResult);
        TestDataUtils.validateAnalysisResult(largeResult);

        // 验证不同尺寸的图像都能正确处理
        assertTrue(smallResult.getProcessingTime() > 0);
        assertTrue(largeResult.getProcessingTime() > 0);
    }

    @Test
    void testConcurrentAnalysis() throws InterruptedException {
        // Given - 创建多个并发分析任务
        int numberOfTasks = 3;
        CompletableFuture<AIAnalysisResult>[] futures = new CompletableFuture[numberOfTasks];

        // When - 并发执行分析
        for (int i = 0; i < numberOfTasks; i++) {
            futures[i] = aiImageAnalysisService.analyzeImage(testImageFile);
        }

        // Then - 等待所有任务完成并验证结果
        for (CompletableFuture<AIAnalysisResult> future : futures) {
            AIAnalysisResult result = future.join();
            assertNotNull(result);
            TestDataUtils.validateAnalysisResult(result);
        }
    }

    @Test
    void testAnalysisResultConsistency() throws ExecutionException, InterruptedException {
        // Given - 对同一图像执行多次分析
        CompletableFuture<AIAnalysisResult> future1 = aiImageAnalysisService.analyzeImage(testImageFile);
        CompletableFuture<AIAnalysisResult> future2 = aiImageAnalysisService.analyzeImage(testImageFile);

        AIAnalysisResult result1 = future1.get();
        AIAnalysisResult result2 = future2.get();

        // Then - 验证结果的一致性（在模拟模式下，结果应该具有相似的结构）
        assertNotNull(result1);
        assertNotNull(result2);
        
        // 验证两次分析都产生了有效结果
        TestDataUtils.validateAnalysisResult(result1);
        TestDataUtils.validateAnalysisResult(result2);
        
        // 验证结果结构一致性
        assertEquals(result1.getLabels().size() >= 3, result2.getLabels().size() >= 3);
        assertEquals(result1.getObjects().size() >= 1, result2.getObjects().size() >= 1);
        
        // 验证置信度都在合理范围内
        assertTrue(result1.getOverallConfidence() >= 0.8);
        assertTrue(result2.getOverallConfidence() >= 0.8);
    }

    @Test
    void testMetadataIntegrity() throws ExecutionException, InterruptedException {
        // When
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then - 验证元数据的完整性
        assertNotNull(result.getMetadata());
        
        // 验证必要的元数据字段
        assertTrue(result.getMetadata().containsKey("file_name"));
        assertTrue(result.getMetadata().containsKey("file_size"));
        assertTrue(result.getMetadata().containsKey("analysis_time"));
        assertTrue(result.getMetadata().containsKey("model_version"));
        
        // 验证元数据值的正确性
        assertEquals(testImageFile.getName(), result.getMetadata().get("file_name"));
        assertEquals(testImageFile.length(), result.getMetadata().get("file_size"));
        assertEquals("mock-v1.0", result.getMetadata().get("model_version"));
        
        // 验证分析时间格式
        assertNotNull(result.getMetadata().get("analysis_time"));
        assertTrue(result.getMetadata().get("analysis_time") instanceof String);
    }
}
