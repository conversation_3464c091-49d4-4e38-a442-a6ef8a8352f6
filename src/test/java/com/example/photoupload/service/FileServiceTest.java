package com.example.photoupload.service;

import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.entity.FileInfo;
import com.example.photoupload.repository.FileInfoRepository;
import com.example.photoupload.service.impl.FileServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 文件服务测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class FileServiceTest {

    @Mock
    private FileInfoRepository fileInfoRepository;

    @Mock
    private ImageProcessingService imageProcessingService;

    @InjectMocks
    private FileServiceImpl fileService;

    private MockMultipartFile testFile;

    @BeforeEach
    void setUp() {
        testFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );
    }

    @Test
    void testUploadFile_Success() throws IOException {
        // Given
        when(fileInfoRepository.findByMd5HashAndIsDeletedFalse(any())).thenReturn(Optional.empty());
        when(fileInfoRepository.save(any(FileInfo.class))).thenAnswer(invocation -> {
            FileInfo fileInfo = invocation.getArgument(0);
            fileInfo.setId(1L);
            return fileInfo;
        });

        // When
        FileUploadResponse response = fileService.uploadFile(testFile, "127.0.0.1");

        // Then
        assertNotNull(response);
        assertEquals("test.jpg", response.getOriginalName());
        assertNotNull(response.getStoredName());
        assertEquals(testFile.getSize(), response.getFileSize());
        verify(fileInfoRepository, times(1)).save(any(FileInfo.class));
    }

    @Test
    void testUploadFile_DuplicateFile() throws IOException {
        // Given
        FileInfo existingFile = new FileInfo();
        existingFile.setId(1L);
        existingFile.setOriginalName("test.jpg");
        existingFile.setStoredName("stored_test.jpg");
        existingFile.setFileSize((long) testFile.getSize());
        
        when(fileInfoRepository.findByMd5HashAndIsDeletedFalse(any())).thenReturn(Optional.of(existingFile));

        // When
        FileUploadResponse response = fileService.uploadFile(testFile, "127.0.0.1");

        // Then
        assertNotNull(response);
        assertEquals("test.jpg", response.getOriginalName());
        assertEquals("stored_test.jpg", response.getStoredName());
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }

    @Test
    void testFileExists_True() {
        // Given
        FileInfo fileInfo = new FileInfo();
        fileInfo.setStoredName("test.jpg");
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(fileInfo));

        // When
        boolean exists = fileService.fileExists("test.jpg");

        // Then
        assertTrue(exists);
    }

    @Test
    void testFileExists_False() {
        // Given
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.empty());

        // When
        boolean exists = fileService.fileExists("test.jpg");

        // Then
        assertFalse(exists);
    }

    @Test
    void testDeleteFile_Success() {
        // Given
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(1L);
        fileInfo.setStoredName("test.jpg");
        fileInfo.setFilePath("/path/to/test.jpg");
        
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(fileInfo));
        when(fileInfoRepository.deleteFileLogically(1L)).thenReturn(1);

        // When
        boolean result = fileService.deleteFile("test.jpg");

        // Then
        assertTrue(result);
        verify(fileInfoRepository, times(1)).deleteFileLogically(1L);
    }
} 