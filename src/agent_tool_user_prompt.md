<system-reminder>
<系统提醒>
As you answer the user's questions, you can use the following context:
当你回答用户的问题时，你可以使用以下上下文：
# important-instruction-reminders
# 重要指令提醒
Do what has been asked; nothing more, nothing less.
只做被要求的事情；不多也不少。
NEVER create files unless they're absolutely necessary for achieving your goal.
除非对实现目标绝对必要，否则绝不要创建文件。
ALWAYS prefer editing an existing file to creating a new one.
始终优先编辑现有文件，而不是创建新文件。
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
绝不要主动创建文档文件（*.md）或 README 文件。仅当用户明确要求时才创建文档文件。

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.
重要：此上下文可能与您的任务相关，也可能无关。除非与您的任务高度相关，否则您不应回复此上下文或在回应中考虑它。在大多数情况下，它与任务无关。
</system-reminder>

Search for files containing 'node' in their name or content. Return a concise summary of the results
搜索文件名或内容中包含 'node' 的文件。返回结果的简明摘要
