[
  ➜ 【数组开始】
  {
    ➜ 【对象开始】
    "type": "function",
    ➜ "type": "function", 【类型：函数】
    "function": {
      ➜ "function": { 【函数定义开始】
      "name": "Task",
      ➜ "name": "Task", 【名称：Task（任务）】
      "description": "Launch a new agent that has access to the following tools: Bash, Glob, Grep, LS, exit_plan_mode, Read, Edit, MultiEdit, Write, NotebookRead, NotebookEdit, WebFetch, TodoRead, TodoWrite, WebSearch. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries, use the Agent tool to perform the search for you.\n\nWhen to use the Agent tool:\n- If you are searching for a keyword like \"config\" or \"logger\", or for questions like \"which file does X?\", the Agent tool is strongly recommended\n\nWhen NOT to use the Agent tool:\n- If you want to read a specific file path, use the Read or Glob tool instead of the Agent tool, to find the match more quickly\n- If you are searching for a specific class definition like \"class Foo\", use the Glob tool instead, to find the match more quickly\n- If you are searching for code within a specific file or set of 2-3 files, use the Read tool instead of the Agent tool, to find the match more quickly\n- Writing code and running bash commands (use other tools for that)\n- Other tasks that are not related to searching for a keyword or file\n\nUsage notes:\n1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses\n2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.\n3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.\n4. The agent's outputs should generally be trusted\n5. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent",
      ➜ "description": "启动一个具有以下工具的新代理：Bash、Glob、Grep、LS、exit_plan_mode、Read、Edit、MultiEdit、Write、NotebookRead、NotebookEdit、WebFetch、TodoRead、TodoWrite、WebSearch。当你搜索关键字或文件且不确定能在前几次尝试中找到正确匹配时，请使用 Agent 工具。\n\n何时使用 Agent 工具：\n- 当你搜索诸如 \"config\"、\"logger\" 等关键字，或想知道 \"哪个文件做了 X？\" 时，强烈建议使用 Agent 工具\n\n不使用 Agent 工具的场景：\n- 如果你要读取特定文件路径，请直接使用 Read 或 Glob 工具，以便更快找到匹配\n- 如果你要查找特定类定义，例如 \"class Foo\"，请使用 Glob 工具\n- 如果你只是在 2-3 个文件范围内查找代码，请使用 Read 工具\n- 编写代码或运行 bash 命令（应使用其他工具）\n- 其他与关键词/文件搜索无关的任务\n\n使用说明：\n1. 尽可能并发启动多个代理以提升性能，可在一条消息中调用多个工具\n2. 代理完成后会返回单条消息，该结果对用户不可见。如需展示，请你再发送精简总结给用户\n3. 每次调用都是无状态的，无法与代理进行后续交互，因此在 prompt 中要写清楚详细任务及希望代理最终返回的信息\n4. 一般可以信任代理输出\n5. 明确告诉代理你期待它写代码还是仅做调研，因为它不了解用户意图"
      "parameters": {
        ➜ "parameters": { 【参数列表开始】
        "type": "object",
        ➜ "type": "object", 【类型：对象】
        "properties": {
          ➜ "properties": { 【属性开始】
          "description": {
            ➜ "description": { 【description 字段】
            "type": "string",
            ➜ "type": "string", 【类型：字符串】
            "description": "A short (3-5 word) description of the task"
            ➜ "description": "任务的简短描述（3-5 个词）"
          },
          "prompt": {
            ➜ "prompt": { 【prompt 字段】
            "type": "string",
            ➜ "type": "string", 【类型：字符串】
            "description": "The task for the agent to perform"
            ➜ "description": "代理要执行的任务"
          }
        },
        ➜ }, 【属性结束】
        "required": [
          ➜ "required": [ 【必填字段列表】
          "description",
          ➜ "description", 【必须包含 description】
          "prompt"
          ➜ "prompt" 【必须包含 prompt】
        ],
        ➜ ], 【必填列表结束】
        "additionalProperties": false,
        ➜ "additionalProperties": false, 【禁止额外属性】
        "$schema": "http://json-schema.org/draft-07/schema#"
        ➜ "$schema": "http://json-schema.org/draft-07/schema#" 【JSON-Schema 版本】
      }
      ➜ } 【parameters 对象结束】
    }
    ➜ } 【function 对象结束】
  },
  ➜ }, 【第一个函数对象结束】
  {
    ➜ { 【第二个函数对象开始】
    "type": "function",
    ➜ "type": "function", 【类型：函数】
    "function": {
      ➜ "function": { 【函数定义开始】
      "name": "Bash",
      ➜ "name": "Bash", 【名称：Bash】
      "description": "Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.\n\nBefore executing the command, please follow these steps:\n\n1. Directory Verification:\n   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location\n   - For example, before running \"mkdir foo/bar\", first use LS to check that \"foo\" exists and is the intended parent directory\n\n2. Command Execution:\n   - Always quote file paths that contain spaces with double quotes (e.g., cd \"path with spaces/file.txt\")\n   - Examples of proper quoting:\n     - cd \"/Users/<USER>/My Documents\" (correct)\n     - cd /Users/<USER>/My Documents (incorrect - will fail)\n     - python \"/path/with spaces/script.py\" (correct)\n     - python /path/with spaces/script.py (incorrect - will fail)\n   - After ensuring proper quoting, execute the command.\n   - Capture the output of the command.\n\nUsage notes:\n  - The command argument is required.\n  - You can specify an optional timeout in milliseconds (up to 600000ms / 10 minutes). If not specified, commands will timeout after 120000ms (2 minutes).\n  - It is very helpful if you write a clear, concise description of what this command does in 5-10 words.\n  - If the output exceeds 30000 characters, output will be truncated before being returned to you.\n  - VERY IMPORTANT: You MUST avoid using search commands like `find` and `grep`. Instead use Grep, Glob, or Task to search. You MUST avoid read tools like `cat`, `head`, `tail`, and `ls`, and use Read and LS to read files.\n  - If you _still_ need to run `grep`, STOP. ALWAYS USE ripgrep at `rg` first, which all Claude Code users have pre-installed.\n  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).\n  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of `cd`. You may use `cd` if the User explicitly requests it.\n    <good-example>\n    pytest /foo/bar/tests\n    </good-example>\n    <bad-example>\n    cd /foo/bar && pytest tests\n    </bad-example>\n",
      ➜ "description": "在持久 shell 会话中执行指定的 bash 命令，可选超时，确保正确处理与安全性。\n\n执行命令前请遵循以下步骤：\n\n1. 目录验证：\n   - 若命令将创建新目录/文件，请先用 LS 工具确认父目录存在且正确\n   - 例如执行 \"mkdir foo/bar\" 前，先用 LS 检查 \"foo\" 是否存在并为目标父目录\n\n2. 命令执行：\n   - 含空格的路径需使用双引号，例如 cd \"path with spaces/file.txt\"\n   - 正确示例：\n     - cd \"/Users/<USER>/My Documents\"（正确）\n     - cd /Users/<USER>/My Documents（错误，无法执行）\n     - python \"/path/with spaces/script.py\"（正确）\n     - python /path/with spaces/script.py（错误）\n   - 确认路径引号后执行命令，并捕获输出。\n\n使用说明：\n  - command 参数必填\n  - 可选 timeout（毫秒，最大 600000）\n  - 请用 5-10 个词简洁描述命令作用\n  - 输出超过 30000 字符将被截断\n  - 重要：避免使用 `find` `grep` 等搜索命令；请用 Grep、Glob 或 Task\n  - 避免直接读取文件，请用 Read 与 LS\n  - 如仍需 grep，请改用 `rg`\n  - 多命令请用 ';' 或 '&&' 分隔，勿使用换行\n  - 尽量保持当前工作目录，除非用户要求，不要使用 `cd`；可使用绝对路径\n    <good-example> pytest /foo/bar/tests </good-example>\n    <bad-example> cd /foo/bar && pytest tests </bad-example>"
      "parameters": {
        ➜ "parameters": { 【参数列表开始】
        "type": "object",
        ➜ "type": "object", 【类型：对象】
        "properties": {
          ➜ "properties": { 【属性开始】
          "command": {
            ➜ "command": { 【command 字段】
            "type": "string",
            ➜ "type": "string", 【类型：字符串】
            "description": "The command to execute"
            ➜ "description": "要执行的命令"
          },
          "timeout": {
            ➜ "timeout": { 【timeout 字段】
            "type": "number",
            ➜ "type": "number", 【类型：数字】
            "description": "Optional timeout in milliseconds (max 600000)"
            ➜ "description": "可选超时（毫秒，最大 600000）"
          },
          "description": {
            ➜ "description": { 【description 字段（嵌套）】
            "type": "string",
            ➜ "type": "string", 【类型：字符串】
            "description": " Clear, concise description of what this command does in 5-10 words. Examples:\nInput: ls\nOutput: Lists files in current directory\n\nInput: git status\nOutput: Shows working tree status\n\nInput: npm install\nOutput: Installs package dependencies\n\nInput: mkdir foo\nOutput: Creates directory 'foo'"
            ➜ "description": "对命令作用的简明描述（5-10 个词）。示例：\n输入：ls —— 列出当前目录文件\n输入：git status —— 显示工作区状态\n输入：npm install —— 安装依赖\n输入：mkdir foo —— 创建目录 foo"
          }
        },
        ➜ }, 【属性结束】
        "required": [
          ➜ "required": [ 【必填字段列表】
          "command"
          ➜ "command" 【必须包含 command】
        ],
        ➜ ],
        "additionalProperties": false,
        ➜ "additionalProperties": false, 【禁止额外属性】
        "$schema": "http://json-schema.org/draft-07/schema#"
        ➜ "$schema": "http://json-schema.org/draft-07/schema#" 【JSON-Schema 版本】
      }
      ➜ } 【parameters 对象结束】
    }
    ➜ } 【function 对象结束】
  },
  ➜ }, 【第二个函数对象结束】
// ... existing code ...
] 