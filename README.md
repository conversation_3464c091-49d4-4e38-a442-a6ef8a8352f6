# Spring Boot 照片上传下载系统

## 项目简介

这是一个基于 Spring Boot 3.x 开发的完整照片上传下载系统，提供了丰富的文件管理功能，包括文件上传、下载、预览、压缩、缩略图生成等核心特性。

## 核心功能

### 🚀 基础功能
- ✅ 单文件和多文件上传
- ✅ 文件类型安全验证（仅支持图片格式）
- ✅ 文件大小限制（最大10MB）
- ✅ 自动生成唯一文件名
- ✅ 文件下载支持断点续传
- ✅ 在线图片预览
- ✅ 文件信息查询

### 🛡️ 安全特性
- ✅ 文件类型真实性检测（Apache Tika）
- ✅ 文件名安全处理
- ✅ 防盗链保护
- ✅ XSS防护
- ✅ 访问权限控制
- ✅ 客户端IP记录

### ⚡ 性能优化
- ✅ 图片自动压缩
- ✅ 缩略图生成
- ✅ 文件去重（MD5哈希）
- ✅ HTTP缓存控制
- ✅ 数据库查询优化

### 📊 管理功能
- ✅ 完善的异常处理
- ✅ 详细的日志记录
- ✅ 存储统计信息
- ✅ 文件清理机制
- ✅ 下载次数统计

## 技术栈

- **Spring Boot 3.2.2**
- **Spring Security** - 安全框架
- **Spring Data JPA** - 数据访问
- **H2 Database** - 内存数据库（可替换）
- **Thumbnailator** - 图片处理
- **Apache Tika** - 文件类型检测
- **SpringDoc OpenAPI** - API文档
- **Maven** - 项目管理

## 快速开始

### 环境要求

- Java 17+
- Maven 3.6+

### 安装运行

1. **克隆项目**
```bash
git clone <repository-url>
cd photo-upload-system
```

2. **编译项目**
```bash
mvn clean compile
```

3. **运行应用**
```bash
mvn spring-boot:run
```

4. **访问应用**
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html
- H2控制台: http://localhost:8080/h2-console

## API接口文档

### 文件上传

#### 单文件上传
```http
POST /api/files/upload
Content-Type: multipart/form-data

file: <文件>
```

**响应示例:**
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "id": 1,
    "originalName": "photo.jpg",
    "storedName": "20231201120000_abc123.jpg",
    "fileSize": 1048576,
    "contentType": "image/jpeg",
    "downloadUrl": "/api/files/download/20231201120000_abc123.jpg",
    "previewUrl": "/api/files/preview/20231201120000_abc123.jpg",
    "thumbnailUrl": "/api/files/thumbnail/20231201120000_abc123_thumb.jpg",
    "isCompressed": true,
    "imageWidth": 1920,
    "imageHeight": 1080,
    "md5Hash": "abc123def456"
  },
  "code": 200,
  "timestamp": 1701419200000
}
```

#### 批量上传
```http
POST /api/files/upload/batch
Content-Type: multipart/form-data

files: <文件数组>
```

### 文件下载

#### 文件下载（支持断点续传）
```http
GET /api/files/download/{fileName}
Range: bytes=0-1023  # 可选，支持断点续传
```

#### 在线预览
```http
GET /api/files/preview/{fileName}
```

#### 获取缩略图
```http
GET /api/files/thumbnail/{fileName}
```

### 文件管理

#### 获取文件信息
```http
GET /api/files/info/{fileName}
```

#### 获取文件列表
```http
GET /api/files/list
```

#### 删除文件
```http
DELETE /api/files/{fileName}
```

#### 批量删除
```http
POST /api/files/delete/batch
Content-Type: application/json

["file1.jpg", "file2.jpg"]
```

#### 获取存储统计
```http
GET /api/files/stats
```

## 配置说明

### 应用配置文件 (application.yml)

```yaml
# 文件存储配置
file:
  upload:
    path: ./uploads/                    # 上传目录
    allowed-types: jpg,jpeg,png,gif,bmp,webp  # 允许的文件类型
    max-file-size: 10485760            # 单文件最大10MB
    max-total-size: 104857600          # 总上传最大100MB
    
    # 图片压缩配置
    compress:
      enabled: true                    # 启用压缩
      quality: 0.8                    # 压缩质量
      max-width: 1920                 # 最大宽度
      max-height: 1080                # 最大高度
      
    # 缩略图配置
    thumbnail:
      enabled: true                    # 启用缩略图
      width: 200                      # 缩略图宽度
      height: 200                     # 缩略图高度
      suffix: "_thumb"                # 缩略图后缀

# 安全配置
security:
  anti-hotlink:
    enabled: true                      # 启用防盗链
    allowed-domains: localhost,127.0.0.1  # 允许的域名
  access:
    auth-required: false              # 是否需要认证
    allowed-ips:                      # 允许的IP（空表示不限制）
```

### 数据库配置

默认使用H2内存数据库，生产环境建议替换为MySQL或PostgreSQL：

```yaml
spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
```

## 项目结构

```
src/
├── main/
│   ├── java/com/example/photoupload/
│   │   ├── config/          # 配置类
│   │   ├── controller/      # 控制器
│   │   ├── dto/            # 数据传输对象
│   │   ├── entity/         # 实体类
│   │   ├── exception/      # 异常处理
│   │   ├── interceptor/    # 拦截器
│   │   ├── repository/     # 数据访问层
│   │   ├── service/        # 业务逻辑层
│   │   └── utils/          # 工具类
│   └── resources/
│       ├── application.yml # 配置文件
│       └── static/         # 静态资源
└── test/                   # 测试代码
```

## 核心类说明

### 实体类
- `FileInfo`: 文件信息实体，包含文件元数据
- `FileUploadResponse`: 文件上传响应DTO
- `FileInfoResponse`: 文件信息响应DTO

### 服务类
- `FileService`: 文件操作核心服务接口
- `FileServiceImpl`: 文件服务实现类
- `ImageProcessingService`: 图片处理服务

### 工具类
- `FileUtils`: 文件操作工具类
- `ApiResponse`: 统一API响应封装

## 安全特性详解

### 1. 文件类型验证
- 扩展名检查
- MIME类型检测
- 文件头验证（Apache Tika）

### 2. 文件名安全
- 危险字符过滤
- 路径遍历防护
- 唯一文件名生成

### 3. 访问控制
- IP白名单/黑名单
- Referer防盗链
- 请求频率限制

### 4. 数据安全
- MD5文件指纹
- 软删除机制
- 访问日志记录

## 性能优化

### 1. 文件处理
- 图片自动压缩
- 缩略图生成
- 文件去重处理

### 2. 缓存策略
- HTTP缓存头设置
- 静态资源缓存
- 数据库查询缓存

### 3. 存储优化
- 分目录存储
- 定期清理机制
- 存储容量监控

## 监控和日志

### 日志配置
- 文件上传/下载日志
- 异常详细记录
- 性能监控日志

### 健康检查
- Spring Boot Actuator
- 存储空间监控
- 数据库连接检查

## 部署说明

### Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/photo-upload-system-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
1. 配置外部数据库
2. 设置文件存储路径
3. 配置反向代理
4. 启用HTTPS
5. 配置日志轮转

## 常见问题

### Q: 如何修改文件大小限制？
A: 在 `application.yml` 中修改 `file.upload.max-file-size` 和 `spring.servlet.multipart.max-file-size`

### Q: 如何添加新的文件类型支持？
A: 在 `application.yml` 中的 `file.upload.allowed-types` 添加新的扩展名

### Q: 如何启用文件访问认证？
A: 设置 `security.access.auth-required: true` 并配置Spring Security

### Q: 如何配置文件存储到云存储？
A: 实现 `FileService` 接口，添加云存储SDK依赖

## 开发指南

### 添加新功能
1. 在相应的Service接口中定义方法
2. 在ServiceImpl中实现业务逻辑
3. 在Controller中添加REST接口
4. 编写单元测试
5. 更新API文档

### 代码规范
- 使用统一的异常处理
- 添加详细的日志记录
- 编写完整的JavaDoc
- 遵循RESTful API设计

## 许可证

本项目采用 MIT 许可证。

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

---

如有问题，请查看 [Wiki](https://github.com/your-repo/wiki) 或提交 [Issue](https://github.com/your-repo/issues)。 