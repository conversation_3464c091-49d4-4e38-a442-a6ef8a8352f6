public class Printer {
    private int num = 1;
    private static final int MAX_NUM = 100;

    // 通过lock协同两个线程交替输出
    private final Object lock = new Object();
    // 1表示printer1线程输出
    private int curPrinter = 1;

    public void printer1() {
        while (num <= MAX_NUM) {
            synchronized (lock) {
                try {
                    while (curPrinter != 1 && num <= MAX_NUM) {
                        lock.wait();
                    }
                    if (num <= MAX_NUM) {
                        System.out.println("Printer1 - " + num);
                        curPrinter = 2;
                        num++;
                        // 通知Printer2
                        lock.notify();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    public void printer2() {
        while (num <= MAX_NUM) {
            synchronized (lock) {
                try {
                    while (curPrinter != 2 && num <= MAX_NUM) {
                        lock.wait();
                    }
                    if (num <= MAX_NUM) {
                        System.out.println("Printer2 - " + num);
                        curPrinter = 1;
                        num++;
                        // 通知Printer1
                        lock.notify();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    public static void main(String[] args) {
        Printer printer = new Printer();
        Thread printer1 = new Thread(() -> printer.printer1());
        Thread printer2 = new Thread(() -> printer.printer2());
        printer1.start();
        printer2.start();
    }
} 