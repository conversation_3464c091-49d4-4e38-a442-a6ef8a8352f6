package main

import (
	"fmt"
	"sync"
)

func findIndex(source, target string) int {
	// 参数验证
	if len(source) == 0 || len(target) == 0 {
		return -1
	}
	
	// 如果目标字符串比源字符串长，直接返回-1
	if len(target) > len(source) {
		return -1
	}

	// 遍历源字符串
	for i := 0; i <= len(source)-len(target); i++ {
		found := true
		// 检查当前位置是否可能匹配
		if source[i] == target[0] {
			// 检查后续字符是否匹配
			for j := 1; j < len(target); j++ {
				if source[i+j] != target[j] {
					found = false
					break
				}
			}
			if found {
				return i
			}
		}
	}
	return -1
}

func main() {
	var (
		num   = 1
		mutex sync.Mutex
		wg    sync.WaitGroup
	)
	
	aChan := make(chan bool)
	bChan := make(chan bool)
	
	wg.Add(2)
	
	// Printer1
	go func() {
		defer wg.Done()
		for {
			<-aChan
			mutex.Lock()
			if num > 100 {
				mutex.Unlock()
				close(bChan)
				return
			}
			fmt.Printf("Printer1 - %d\n", num)
			num++
			mutex.Unlock()
			bChan <- true
		}
	}()
	
	// Printer2
	go func() {
		defer wg.Done()
		for {
			<-bChan
			mutex.Lock()
			if num > 100 {
				mutex.Unlock()
				close(aChan)
				return
			}
			fmt.Printf("Printer2 - %d\n", num)
			num++
			mutex.Unlock()
			aChan <- true
		}
	}()
	
	// 启动打印
	aChan <- true
	wg.Wait()

	str := "abcabdcabxcabccdef"
	subStr := "cabx"
	index := findIndex(str, subStr)
	fmt.Printf("第一次匹配的位置是: %d\n", index)
} 